// lib/presentation/pages/improved_ad_details_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/validators/input_validators.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import '../widgets/ad_creation_navigation_buttons.dart';
import '../widgets/improved_ad_creation_progress.dart';
import 'improved_ad_settings_page.dart';

/// صفحة تفاصيل الإعلان المحسنة
class ImprovedAdDetailsPage extends StatefulWidget {
  final Estate estate;
  final bool isEditing;

  const ImprovedAdDetailsPage(
      {super.key, required this.estate, this.isEditing = false});

  @override
  State<ImprovedAdDetailsPage> createState() => _ImprovedAdDetailsPageState();
}

class _ImprovedAdDetailsPageState extends State<ImprovedAdDetailsPage>
    with TickerProviderStateMixin {
  // خدمة المسودات المحسنة
  final _draftService = EnhancedAdDraftService();

  // مفتاح النموذج للتحقق من صحة البيانات
  final _formKey = GlobalKey<FormState>();

  // وحدات التحكم في النص للحقول الأساسية
  final _titleController = TextEditingController();
  final _priceController = TextEditingController();
  final _descriptionController = TextEditingController();

  // وحدات التحكم في النص للتفاصيل الداخلية
  final _reboundController = TextEditingController();
  final _numberOfRoomsController = TextEditingController();
  final _salonController = TextEditingController();
  final _areaController = TextEditingController();
  final _floorNumberController = TextEditingController();
  final _numberOfBathroomsController = TextEditingController();
  final _buildingAgeController = TextEditingController();
  final _numberOfFloorsController = TextEditingController();
  final _discountCodeController = TextEditingController();

  // متغيرات الموقع
  String _governorate = "محافظة العاصمة";
  String _city = "مدينة الكويت";
  int _piece = 1;

  // قوائم الاختيارات - البيانات الرسمية المحدثة
  final List<String> _governorates = [
    "محافظة العاصمة",
    "محافظة حولي",
    "محافظة مبارك الكبير",
    "محافظة الأحمدي",
    "محافظة الفروانية",
    "محافظة الجهراء",
  ];

  final List<String> _cities = [
    // مناطق محافظة العاصمة
    "مدينة الكويت",
    "دسمان",
    "الشرق",
    "المرقاب",
    "الصوابر",
    "القبلة",
    "الصالحية",
    "بنيد القار",
    "الدوحة",
    "الدسمة",
    "الدعية",
    "الفيحاء",
    "جبلة",
    "كيفان",
    "الخالدية",
    "المنصورية",
    "النزهة",
    "القادسية",
    "قرطبة",
    "الروضة",
    "الشامية",
    "الشويخ",
    "الصليبيخات",
    "السرة",
    "العديلية",
    "غرناطة",
    "النهضة",
    "اليرموك",
    "القيروان",
    "مدينة جابر الأحمد",
    "الري",
    "جزيرة فيلكا",

    // مناطق محافظة حولي
    "حولي",
    "السالمية",
    "الجابرية",
    "الرميثية",
    "سلوى",
    "بيان",
    "مشرف",
    "الشعب",
    "الشهداء",
    "حطين",
    "سلام",
    "الزهراء",
    "مبارك العبدالله",
    "الصديق",
    "البدع",
    "أنجفة",

    // مناطق محافظة الفروانية
    "الفروانية",
    "عبد الله المبارك",
    "الأندلس",
    "العارضية",
    "إشبيلية",
    "الضجيج",
    "الفردوس",
    "جليب الشيوخ",
    "خيطان",
    "العمرية",
    "الرابية",
    "الرقعي",
    "الرحاب",
    "صباح الناصر",

    // مناطق محافظة الأحمدي
    "الأحمدي",
    "أبو حليفة",
    "علي صباح السالم",
    "الفحيحيل",
    "المنقف",
    "الفنطاس",
    "المهبولة",
    "الخيران",
    "صباح الأحمد",
    "الصباحية",
    "الوفرة",
    "الزور",
    "هدية",
    "جابر العلي",
    "الجليعة",
    "النويصيب",
    "الرقة",

    // مناطق محافظة الجهراء
    "الجهراء",
    "العبدلي",
    "المطلاع",
    "كاظمة",
    "بحرة",
    "كبد",
    "الشقايا",
    "النهضة",
    "النعيم",
    "النسيم",
    "العيون",
    "القصر",
    "جابر الأحمد",
    "سعد العبد الله",
    "الصبية",
    "الصليبية",
    "تيماء",
    "الواحة",
    "جزيرة بوبيان",
    "جزيرة وربة",

    // مناطق محافظة مبارك الكبير
    "مبارك الكبير",
    "صباح السالم",
    "العدان",
    "القرين",
    "القصور",
    "الفنيطيس",
    "المسيلة",
    "أبو الحصانية",
    "أبو فطيرة",
    "المسايل",
    "وسطي",
  ];

  final List<int> _pieces = List.generate(9, (i) => i + 1);

  // متغيرات التفاصيل الداخلية
  String _selectedInternalLocation = "شارع واحد";
  String _selectedPropertyType = "سكني";
  String _selectedUsageType = "للبيع"; // نوع الاستغلال

  // قوائم الاختيارات للتفاصيل الداخلية
  final List<String> _internalLocations = [
    "شارع واحد",
    "شارعين",
    "زاوية",
    "داخلي",
  ];

  final List<String> _propertyTypes = [
    "سكني",
    "استثماري",
    "تجاري",
    "صناعي",
    "زراعي",
  ];

  final List<String> _usageTypes = [
    "للبيع",
    "للإيجار",
    "للبدل",
    "للاستثمار",
  ];

  // متغيرات التجهيزات الإضافية
  bool _hasCentralAC = false;
  bool _hasSecurity = false;
  bool _allowPets = false;
  bool _hasElevator = false;
  bool _hasSwimmingPool = false;
  bool _hasMaidRoom = false;
  bool _hasGarage = false;
  bool _hasBalcony = false;
  bool _isFullyFurnished = false;

  // متغيرات الرسوم المتحركة
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // متغيرات الحالة
  bool _isLoading = false;
  int _activeTabIndex = 0;

  // متغيرات أخطاء التحقق
  String? _titleError;
  String? _priceError;
  String? _descriptionError;
  String? _locationError;

  @override
  void initState() {
    super.initState();

    // إعداد وحدات التحكم في التاب
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    _animationController.forward();

    // تحميل المسودة
    _loadDraft();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _animationController.dispose();

    // التخلص من وحدات التحكم في النص
    _titleController.dispose();
    _priceController.dispose();
    _descriptionController.dispose();
    _reboundController.dispose();
    _numberOfRoomsController.dispose();
    _salonController.dispose();
    _areaController.dispose();
    _floorNumberController.dispose();
    _numberOfBathroomsController.dispose();
    _buildingAgeController.dispose();
    _numberOfFloorsController.dispose();
    _discountCodeController.dispose();

    super.dispose();
  }

  /// تحميل المسودة
  Future<void> _loadDraft() async {
    setState(() {
      _isLoading = true;
    });

    final lastDraft = await _draftService.getLastDraft();
    if (lastDraft != null && mounted) {
      // تحميل البيانات الأساسية
      _titleController.text = lastDraft['title'] ?? '';
      _priceController.text = lastDraft['price']?.toString() ?? '';
      _descriptionController.text = lastDraft['description'] ?? '';

      // تحميل بيانات الموقع
      _governorate = lastDraft['governorate'] ?? _governorate;
      _city = lastDraft['city'] ?? _city;
      _piece = lastDraft['piece'] ?? _piece;

      // تحميل التفاصيل الداخلية
      _reboundController.text = lastDraft['rebound'] ?? '';
      _numberOfRoomsController.text =
          lastDraft['numberOfRooms']?.toString() ?? '';
      _salonController.text = lastDraft['salon'] ?? '';
      _areaController.text = lastDraft['area']?.toString() ?? '';
      _floorNumberController.text = lastDraft['floorNumber']?.toString() ?? '';
      _numberOfBathroomsController.text =
          lastDraft['numberOfBathrooms']?.toString() ?? '';
      _buildingAgeController.text = lastDraft['buildingAge']?.toString() ?? '';
      _numberOfFloorsController.text =
          lastDraft['numberOfFloors']?.toString() ?? '';

      // تحميل الاختيارات
      _selectedInternalLocation =
          lastDraft['internalLocation'] ?? _selectedInternalLocation;
      _selectedPropertyType =
          lastDraft['propertyType'] ?? _selectedPropertyType;
      _selectedUsageType =
          lastDraft['usageType'] ?? _selectedUsageType;

      // تحميل التجهيزات الإضافية
      _hasCentralAC = lastDraft['hasCentralAC'] ?? false;
      _hasSecurity = lastDraft['hasSecurity'] ?? false;
      _allowPets = lastDraft['allowPets'] ?? false;
      _hasElevator = lastDraft['hasElevator'] ?? false;
      _hasSwimmingPool = lastDraft['hasSwimmingPool'] ?? false;
      _hasMaidRoom = lastDraft['hasMaidRoom'] ?? false;
      _hasGarage = lastDraft['hasGarage'] ?? false;
      _hasBalcony = lastDraft['hasBalcony'] ?? false;
      _isFullyFurnished = lastDraft['isFullyFurnished'] ?? false;
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// معالجة تغيير التاب
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _activeTabIndex = _tabController.index;
      });

      // حفظ المسودة عند تغيير التاب
      _saveDraft();
    }
  }

  /// حفظ المسودة
  Future<void> _saveDraft() async {
    // جمع البيانات
    final draftData = {
      // البيانات الأساسية
      'title': _titleController.text,
      'price': double.tryParse(_priceController.text) ?? 0.0,
      'description': _descriptionController.text,

      // بيانات الموقع
      'governorate': _governorate,
      'city': _city,
      'piece': _piece,

      // التفاصيل الداخلية
      'rebound': _reboundController.text,
      'numberOfRooms': int.tryParse(_numberOfRoomsController.text),
      'salon': _salonController.text,
      'area': double.tryParse(_areaController.text),
      'floorNumber': int.tryParse(_floorNumberController.text),
      'numberOfBathrooms': int.tryParse(_numberOfBathroomsController.text),
      'buildingAge': int.tryParse(_buildingAgeController.text),
      'numberOfFloors': int.tryParse(_numberOfFloorsController.text),
      'internalLocation': _selectedInternalLocation,
      'propertyType': _selectedPropertyType,
      'usageType': _selectedUsageType,

      // التجهيزات الإضافية
      'hasCentralAC': _hasCentralAC,
      'hasSecurity': _hasSecurity,
      'allowPets': _allowPets,
      'hasElevator': _hasElevator,
      'hasSwimmingPool': _hasSwimmingPool,
      'hasMaidRoom': _hasMaidRoom,
      'hasGarage': _hasGarage,
      'hasBalcony': _hasBalcony,
      'isFullyFurnished': _isFullyFurnished,

      // الخطوة الحالية
      'step': 3,
    };

    // حفظ المسودة
    await _draftService.saveDraft(draftData);
  }

  /// حفظ المسودة مع التفاصيل الكاملة
  void _saveDraftWithDetails() {
    final adState = context.read<ImprovedAdBloc>().state;

    _draftService.autoSaveDraft({
      'mainCategory': adState.mainCategory,
      'subCategory': adState.subCategory,
      'imagePaths': adState.imagePaths,
      'title': _titleController.text.trim(),
      'description': _descriptionController.text.trim(),
      'price': double.tryParse(_priceController.text) ?? 0.0,
      'governorate': _governorate,
      'city': _city,
      'piece': _piece,
      'usageType': _selectedUsageType,
      'area': double.tryParse(_areaController.text),
      'numberOfRooms': int.tryParse(_numberOfRoomsController.text),
      'numberOfBathrooms': int.tryParse(_numberOfBathroomsController.text),
      'floorNumber': int.tryParse(_floorNumberController.text),
      'buildingAge': int.tryParse(_buildingAgeController.text),
      'numberOfFloors': int.tryParse(_numberOfFloorsController.text),
      'propertyType': _selectedPropertyType,
      'rebound': _reboundController.text,
      'internalLocation': _selectedInternalLocation,
      'salon': _salonController.text,
      'hasCentralAC': _hasCentralAC,
      'hasSecurity': _hasSecurity,
      'allowPets': _allowPets,
      'hasElevator': _hasElevator,
      'hasSwimmingPool': _hasSwimmingPool,
      'hasMaidRoom': _hasMaidRoom,
      'hasGarage': _hasGarage,
      'hasBalcony': _hasBalcony,
      'isFullyFurnished': _isFullyFurnished,
      'step': 3,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// التحقق من صحة البيانات وإرسالها
  /// التحقق من صحة البيانات الأساسية
  bool _validateBasicDetails() {
    bool isValid = true;

    // التحقق من العنوان
    if (_titleController.text.trim().isEmpty) {
      setState(() {
        _titleError = "يرجى إدخال عنوان الإعلان";
      });
      isValid = false;
    } else if (_titleController.text.trim().length < 5) {
      setState(() {
        _titleError = "يجب أن يكون العنوان أكثر من 5 أحرف";
      });
      isValid = false;
    } else {
      setState(() {
        _titleError = null;
      });
    }

    // التحقق من السعر
    if (_priceController.text.trim().isEmpty) {
      setState(() {
        _priceError = "يرجى إدخال السعر";
      });
      isValid = false;
    } else {
      try {
        double price = double.parse(_priceController.text.trim());
        if (price <= 0) {
          setState(() {
            _priceError = "يجب أن يكون السعر أكبر من صفر";
          });
          isValid = false;
        } else if (price > 10000000) {
          setState(() {
            _priceError = "السعر مرتفع جداً، يرجى التحقق";
          });
          isValid = false;
        } else {
          setState(() {
            _priceError = null;
          });
        }
      } catch (e) {
        setState(() {
          _priceError = "يرجى إدخال سعر صحيح";
        });
        isValid = false;
      }
    }

    // التحقق من الوصف
    if (_descriptionController.text.trim().isEmpty) {
      setState(() {
        _descriptionError = "يرجى إدخال وصف الإعلان";
      });
      isValid = false;
    } else if (_descriptionController.text.trim().length < 20) {
      setState(() {
        _descriptionError = "يجب أن يكون الوصف أكثر من 20 حرف";
      });
      isValid = false;
    } else {
      setState(() {
        _descriptionError = null;
      });
    }

    return isValid;
  }

  void _submitForm() {
    // التحقق من صحة النموذج والبيانات الأساسية
    if (_formKey.currentState!.validate() && _validateBasicDetails()) {
      _formKey.currentState!.save();

      // حفظ البيانات في BLoC

      // إرسال نوع الاستغلال
      context.read<ImprovedAdBloc>().add(
            SetUsageType(_selectedUsageType));

      // إرسال البيانات الأساسية
      context.read<ImprovedAdBloc>().add(
            SetBasicDetails(
              title: _titleController.text.trim(),
              price: double.parse(_priceController.text.trim()),
              governorate: _governorate,
              city: _city,
              piece: _piece,
              description: _descriptionController.text.trim()));

      // إرسال التفاصيل الداخلية
      context.read<ImprovedAdBloc>().add(
            SetInternalDetails(
              rebound: _reboundController.text.trim(),
              numberOfRooms: int.tryParse(_numberOfRoomsController.text.trim()),
              internalLocation: _selectedInternalLocation,
              salon: _salonController.text.trim(),
              area: double.tryParse(_areaController.text.trim()),
              floorNumber: int.tryParse(_floorNumberController.text.trim()),
              numberOfBathrooms:
                  int.tryParse(_numberOfBathroomsController.text.trim()),
              buildingAge: int.tryParse(_buildingAgeController.text.trim()),
              numberOfFloors:
                  int.tryParse(_numberOfFloorsController.text.trim()),
              propertyType: _selectedPropertyType));

      // إرسال التجهيزات الإضافية
      context.read<ImprovedAdBloc>().add(
            SetExtraFeatures(
              autoRepublish: false,
              kuwaitCornersPin: false,
              movingAd: false,
              vipBadge: false,
              pinnedOnHome: false,
              discountCode: _discountCodeController.text.trim().isEmpty
                  ? null
                  : _discountCodeController.text.trim(),
              hasGarage: _hasGarage,
              hasCentralAC: _hasCentralAC,
              hasMaidRoom: _hasMaidRoom,
              isFullyFurnished: _isFullyFurnished,
              hasSecurity: _hasSecurity,
              allowPets: _allowPets,
              hasElevator: _hasElevator,
              hasSwimmingPool: _hasSwimmingPool,
              hasBalcony: _hasBalcony));

      // حفظ المسودة مع التفاصيل الكاملة
      _saveDraftWithDetails();

      // الانتقال إلى الصفحة التالية
      if (widget.isEditing) {
        // في حالة التحرير، نعود إلى صفحة عقاراتي بعد حفظ التغييرات
        // تحديث العقار في قاعدة البيانات
        context.read<ImprovedAdBloc>().add(UpdateEstate(widget.estate.id));

        // العودة إلى الصفحة السابقة
        Navigator.pop(context);
        Navigator.pop(context);
      } else {
        // في حالة الإنشاء، ننتقل إلى صفحة الإعدادات
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => ImprovedAdSettingsPage(estate: widget.estate)));
      }
    } else {
      // التنقل إلى التاب الذي يحتوي على أخطاء
      if (_activeTabIndex != 0) {
        _tabController.animateTo(0);
      }

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "يرجى ملء جميع الحقول المطلوبة بشكل صحيح",
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
    }
  }

  /// بناء تاب المعلومات الأساسية
  Widget _buildBasicInfoTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              "المعلومات الأساسية",
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor)),
            const SizedBox(height: 16),

            // عنوان الإعلان
            TextFormField(
              controller: _titleController,
              maxLength: 70,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "عنوان الإعلان",
                hintText: "مثال: منزل فاخر في منطقة السالمية",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.title),
                labelStyle: GoogleFonts.cairo(),
                errorText: _titleError,
                errorStyle: GoogleFonts.cairo(color: Colors.red)),
              validator: InputValidators.validateTitle,
              onChanged: (value) {
                // إعادة التحقق عند تغيير القيمة
                if (_titleError != null) {
                  setState(() {
                    _titleError = null;
                  });
                }
              }),
            const SizedBox(height: 16),

            // السعر
            TextFormField(
              controller: _priceController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "السعر (د.ك)",
                hintText: "مثال: 150000",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.attach_money),
                labelStyle: GoogleFonts.cairo(),
                errorText: _priceError,
                errorStyle: GoogleFonts.cairo(color: Colors.red)),
              validator: InputValidators.validatePrice,
              onChanged: (value) {
                // إعادة التحقق عند تغيير القيمة
                if (_priceError != null) {
                  setState(() {
                    _priceError = null;
                  });
                }
              }),
            const SizedBox(height: 16),

            // نوع الاستغلال
            DropdownButtonFormField<String>(
              value: _selectedUsageType,
              decoration: InputDecoration(
                labelText: "نوع الاستغلال",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.business_center),
                labelStyle: GoogleFonts.cairo()),
              items: _usageTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type,
                  child: Text(type, style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedUsageType = value;
                  });
                }
              }),
            const SizedBox(height: 16),

            // الموقع
            Text(
              "الموقع",
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),

            // المحافظة
            DropdownButtonFormField<String>(
              value: _governorate,
              decoration: InputDecoration(
                labelText: "المحافظة",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.location_city),
                labelStyle: GoogleFonts.cairo()),
              items: _governorates.map((gov) {
                return DropdownMenuItem<String>(
                  value: gov,
                  child: Text(gov, style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _governorate = value;
                  });
                }
              }),
            const SizedBox(height: 16),

            // المدينة
            DropdownButtonFormField<String>(
              value: _city,
              decoration: InputDecoration(
                labelText: "المدينة",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.location_on),
                labelStyle: GoogleFonts.cairo()),
              items: _cities.map((city) {
                return DropdownMenuItem<String>(
                  value: city,
                  child: Text(city, style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _city = value;
                  });
                }
              }),
            const SizedBox(height: 16),

            // القطعة
            DropdownButtonFormField<int>(
              value: _piece,
              decoration: InputDecoration(
                labelText: "القطعة",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.grid_on),
                labelStyle: GoogleFonts.cairo()),
              items: _pieces.map((piece) {
                return DropdownMenuItem<int>(
                  value: piece,
                  child: Text(piece.toString(), style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _piece = value;
                  });
                }
              }),
            const SizedBox(height: 16),

            // الوصف
            TextFormField(
              controller: _descriptionController,
              maxLines: 5,
              maxLength: 1000,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "وصف العقار",
                hintText: "اكتب وصفاً تفصيلياً للعقار...",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                alignLabelWithHint: true,
                labelStyle: GoogleFonts.cairo(),
                errorText: _descriptionError,
                errorStyle: GoogleFonts.cairo(color: Colors.red)),
              validator: InputValidators.validateDescription,
              onChanged: (value) {
                // إعادة التحقق عند تغيير القيمة
                if (_descriptionError != null) {
                  setState(() {
                    _descriptionError = null;
                  });
                }
              }),
            const SizedBox(height: 24),

            // نصائح لكتابة وصف جيد
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: Colors.amber.shade700),
                        const SizedBox(width: 8),
                        Text(
                          "نصائح لكتابة وصف جذاب",
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700)),
                      ]),
                    const SizedBox(height: 8),
                    Text(
                      "• اذكر المميزات الفريدة للعقار\n"
                      "• حدد المرافق القريبة (مدارس، أسواق، مساجد)\n"
                      "• اشرح سبب تميز الموقع\n"
                      "• تجنب المبالغة في الوصف",
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.blue.shade900,
                        height: 1.3)),
                  ]))),
          ])));
  }

  /// بناء تاب تفاصيل العقار
  Widget _buildPropertyDetailsTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              "تفاصيل العقار",
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor)),
            const SizedBox(height: 16),

            // المساحة
            TextFormField(
              controller: _areaController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "المساحة (م²)",
                hintText: "مثال: 250",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.square_foot),
                labelStyle: GoogleFonts.cairo())),
            const SizedBox(height: 16),

            // عدد الغرف
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _numberOfRoomsController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    style: GoogleFonts.cairo(fontSize: 14),
                    decoration: InputDecoration(
                      labelText: "عدد الغرف",
                      hintText: "مثال: 3",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      prefixIcon: const Icon(Icons.bed),
                      labelStyle: GoogleFonts.cairo()))),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _numberOfBathroomsController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    style: GoogleFonts.cairo(fontSize: 14),
                    decoration: InputDecoration(
                      labelText: "عدد الحمامات",
                      hintText: "مثال: 2",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      prefixIcon: const Icon(Icons.bathtub),
                      labelStyle: GoogleFonts.cairo()))),
              ]),
            const SizedBox(height: 16),

            // الصالون
            TextFormField(
              controller: _salonController,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "الصالون",
                hintText: "مثال: صالون كبير مع إطلالة",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.weekend),
                labelStyle: GoogleFonts.cairo())),
            const SizedBox(height: 16),

            // الارتداد
            TextFormField(
              controller: _reboundController,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "الارتداد",
                hintText: "مثال: 3 متر من الجهات الأربعة",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.border_outer),
                labelStyle: GoogleFonts.cairo())),
            const SizedBox(height: 16),

            // الطابق وعدد الطوابق
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _floorNumberController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    style: GoogleFonts.cairo(fontSize: 14),
                    decoration: InputDecoration(
                      labelText: "رقم الطابق",
                      hintText: "مثال: 2",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      prefixIcon: const Icon(Icons.layers),
                      labelStyle: GoogleFonts.cairo()))),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _numberOfFloorsController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    style: GoogleFonts.cairo(fontSize: 14),
                    decoration: InputDecoration(
                      labelText: "عدد الطوابق",
                      hintText: "مثال: 3",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                      prefixIcon: const Icon(Icons.stairs),
                      labelStyle: GoogleFonts.cairo()))),
              ]),
            const SizedBox(height: 16),

            // عمر البناء
            TextFormField(
              controller: _buildingAgeController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "عمر البناء (سنوات)",
                hintText: "مثال: 5",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.calendar_today),
                labelStyle: GoogleFonts.cairo())),
            const SizedBox(height: 16),

            // الموقع الداخلي
            DropdownButtonFormField<String>(
              value: _selectedInternalLocation,
              decoration: InputDecoration(
                labelText: "الموقع الداخلي",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.location_searching),
                labelStyle: GoogleFonts.cairo()),
              items: _internalLocations.map((location) {
                return DropdownMenuItem<String>(
                  value: location,
                  child: Text(location, style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedInternalLocation = value;
                  });
                }
              }),
            const SizedBox(height: 16),

            // نوع العقار
            DropdownButtonFormField<String>(
              value: _selectedPropertyType,
              decoration: InputDecoration(
                labelText: "نوع العقار",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.home_work),
                labelStyle: GoogleFonts.cairo()),
              items: _propertyTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type,
                  child: Text(type, style: GoogleFonts.cairo()));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPropertyType = value;
                  });
                }
              }),
          ])));
  }

  /// بناء تاب المميزات
  Widget _buildFeaturesTab() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              "مميزات العقار",
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor)),
            const SizedBox(height: 16),

            // المميزات
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                _buildFeatureCard(
                  title: "تكييف مركزي",
                  icon: Icons.ac_unit,
                  isSelected: _hasCentralAC,
                  onTap: () {
                    setState(() {
                      _hasCentralAC = !_hasCentralAC;
                    });
                  }),
                _buildFeatureCard(
                  title: "نظام أمان",
                  icon: Icons.security,
                  isSelected: _hasSecurity,
                  onTap: () {
                    setState(() {
                      _hasSecurity = !_hasSecurity;
                    });
                  }),
                _buildFeatureCard(
                  title: "يسمح بالحيوانات",
                  icon: Icons.pets,
                  isSelected: _allowPets,
                  onTap: () {
                    setState(() {
                      _allowPets = !_allowPets;
                    });
                  }),
                _buildFeatureCard(
                  title: "مصعد",
                  icon: Icons.elevator,
                  isSelected: _hasElevator,
                  onTap: () {
                    setState(() {
                      _hasElevator = !_hasElevator;
                    });
                  }),
                _buildFeatureCard(
                  title: "حمام سباحة",
                  icon: Icons.pool,
                  isSelected: _hasSwimmingPool,
                  onTap: () {
                    setState(() {
                      _hasSwimmingPool = !_hasSwimmingPool;
                    });
                  }),
                _buildFeatureCard(
                  title: "غرفة خادمة",
                  icon: Icons.person,
                  isSelected: _hasMaidRoom,
                  onTap: () {
                    setState(() {
                      _hasMaidRoom = !_hasMaidRoom;
                    });
                  }),
                _buildFeatureCard(
                  title: "مرآب",
                  icon: Icons.garage,
                  isSelected: _hasGarage,
                  onTap: () {
                    setState(() {
                      _hasGarage = !_hasGarage;
                    });
                  }),
                _buildFeatureCard(
                  title: "شرفة",
                  icon: Icons.balcony,
                  isSelected: _hasBalcony,
                  onTap: () {
                    setState(() {
                      _hasBalcony = !_hasBalcony;
                    });
                  }),
                _buildFeatureCard(
                  title: "مفروش بالكامل",
                  icon: Icons.chair,
                  isSelected: _isFullyFurnished,
                  onTap: () {
                    setState(() {
                      _isFullyFurnished = !_isFullyFurnished;
                    });
                  }),
              ]),
            const SizedBox(height: 24),

            // كود الخصم
            TextFormField(
              controller: _discountCodeController,
              textInputAction: TextInputAction.done,
              style: GoogleFonts.cairo(fontSize: 14),
              decoration: InputDecoration(
                labelText: "كود الخصم (اختياري)",
                hintText: "أدخل كود الخصم إن وجد",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8)),
                prefixIcon: const Icon(Icons.discount),
                labelStyle: GoogleFonts.cairo())),
          ])));
  }

  /// بناء بطاقة ميزة
  Widget _buildFeatureCard({
    required String title,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width / 2 - 24,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.shade300,
            width: isSelected ? 2 : 1)),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade200,
                shape: BoxShape.circle),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey.shade600,
                size: 20)),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.black87))),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20),
          ])));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم وأزرار التنقل أسفل الصفحة
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: _submitForm,
            onBack: () => Navigator.pop(context),
            nextText: "متابعة",
            backText: "العودة",
            isNextDisabled: false),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 3,
            onStepTap: (step) {
              if (step < 3) {
                Navigator.pop(context);
              }
            }),
        ]),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: Column(
                  children: [
                    // ترويسة مع زر رجوع وعنوان
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.black87),
                            onPressed: () => Navigator.of(context).pop()),
                          const SizedBox(width: 8),
                          Text(
                            "تفاصيل العقار",
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87)),
                        ])),

                    // شريط التاب
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 5,
                            offset: const Offset(0, 2)),
                        ]),
                      child: TabBar(
                        controller: _tabController,
                        labelColor: Theme.of(context).primaryColor,
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: Theme.of(context).primaryColor,
                        tabs: [
                          Tab(
                            icon: const Icon(Icons.description),
                            text: "المعلومات الأساسية",
                            height: 60,
                            iconMargin: const EdgeInsets.only(bottom: 4)),
                          Tab(
                            icon: const Icon(Icons.home),
                            text: "تفاصيل العقار",
                            height: 60,
                            iconMargin: const EdgeInsets.only(bottom: 4)),
                          Tab(
                            icon: const Icon(Icons.settings),
                            text: "المميزات",
                            height: 60,
                            iconMargin: const EdgeInsets.only(bottom: 4)),
                        ])),

                    // محتوى التاب
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // التاب الأول: المعلومات الأساسية
                          _buildBasicInfoTab(),

                          // التاب الثاني: تفاصيل العقار
                          _buildPropertyDetailsTab(),

                          // التاب الثالث: المميزات
                          _buildFeaturesTab(),
                        ])),
                  ]))));
  }
}
