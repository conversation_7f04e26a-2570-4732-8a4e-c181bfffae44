// lib/presentation/widgets/estate_details_page.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:math';

import '../../core/theme/app_colors.dart';
import '../../core/constants/user_types.dart';

import '../../domain/entities/estate.dart';
import '../../domain/models/property_request/property_request_model.dart';
import '../../core/services/property_request_service.dart';
import '../../data/repositories_impl/property_request_repository_impl.dart';
import '../../core/services/messaging_service.dart';
import '../../data/repositories_impl/messaging_repository_impl.dart';
import '../bloc/estate_bloc.dart';
import '../bloc/estate_event.dart';

// الويدجت الجديدة للتقييمات والجولات الافتراضية
import 'package:kuwait_corners/presentation/widgets/rating_widget.dart';
import 'package:kuwait_corners/presentation/widgets/virtual_tour_viewer.dart';
import 'package:kuwait_corners/core/services/rating_review_service.dart';
import 'package:kuwait_corners/core/routes/app_routes.dart';

/// صفحة تعرض تفاصيل الإعلان بالكامل.
class EstateDetailsPage extends StatefulWidget {
  final Estate estate;

  const EstateDetailsPage({super.key, required this.estate});

  @override
  State<EstateDetailsPage> createState() => _EstateDetailsPageState();
}

class _EstateDetailsPageState extends State<EstateDetailsPage> {
  // متغير لتتبع الصفحة الحالية في عارض الصور
  int _currentImageIndex = 0;

  // تحكم في عارض الصور
  final PageController _pageController = PageController();

  // تحكم في التمرير للصفحة
  final ScrollController _scrollController = ScrollController();

  // متغير لتتبع حالة التمرير
  bool _isCollapsed = false;

  // Variable para almacenar el estado de carga
  bool _isLoading = true;
  // Variable para almacenar el anuncio actualizado
  late Estate _estate;

  // متغير لتخزين نوع المستخدم الحالي
  String? _currentUserType;

  // متغير لتخزين معرف المستخدم الحالي
  String? _currentUserId;

  // بيانات المستخدم صاحب الإعلان
  Map<String, dynamic>? _advertiserData;
  bool _isLoadingAdvertiserData = true;
  String? _advertiserPhoneNumber;
  bool _canShowPhone = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Inicializar el anuncio con los datos proporcionados
    _estate = widget.estate;

    // Cargar datos completos desde Firebase
    _loadEstateData();

    // تحميل بيانات المستخدم الحالي
    _loadCurrentUserData();

    // تحميل بيانات المستخدم صاحب الإعلان
    _loadAdvertiserData();
  }

  // تحميل بيانات المستخدم الحالي لتحديد نوع المستخدم
  Future<void> _loadCurrentUserData() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        _currentUserId = user.uid;

        // الحصول على بيانات المستخدم من Firestore
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          setState(() {
            _currentUserType = userData['userType'] ?? 'property_seeker';
          });
        } else {
          // إذا لم توجد بيانات المستخدم، افتراض أنه باحث عن عقار
          setState(() {
            _currentUserType = 'property_seeker';
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      // في حالة حدوث خطأ، افتراض أنه باحث عن عقار
      setState(() {
        _currentUserType = 'property_seeker';
      });
    }
  }

  // تحميل بيانات المستخدم صاحب الإعلان
  Future<void> _loadAdvertiserData() async {
    try {
      // التحقق من وجود معرف المالك
      if (widget.estate.ownerId == null || widget.estate.ownerId!.isEmpty) {
        setState(() {
          _isLoadingAdvertiserData = false;
          _canShowPhone = false;
        });
        return;
      }

      // الحصول على بيانات المستخدم صاحب الإعلان من Firestore
      final advertiserDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(widget.estate.ownerId)
          .get();

      if (advertiserDoc.exists) {
        final advertiserData = advertiserDoc.data() as Map<String, dynamic>;

        // جلب عدد الإعلانات الفعلي للمعلن
        final advertiserEstatesSnapshot = await FirebaseFirestore.instance
            .collection('estates')
            .where('ownerId', isEqualTo: widget.estate.ownerId)
            .get();

        final actualAdsCount = advertiserEstatesSnapshot.docs.length;

        // جلب تاريخ إنشاء الحساب
        DateTime? accountCreationDate;
        if (advertiserData['createdAt'] != null) {
          if (advertiserData['createdAt'] is Timestamp) {
            accountCreationDate = (advertiserData['createdAt'] as Timestamp).toDate();
          } else if (advertiserData['createdAt'] is String) {
            try {
              accountCreationDate = DateTime.parse(advertiserData['createdAt']);
            } catch (e) {
              debugPrint('Error parsing createdAt string: $e');
            }
          }
        }

        setState(() {
          _advertiserData = advertiserData;
          _advertiserPhoneNumber = advertiserData['phoneNumber'] ?? advertiserData['phone'];

          // تحديث بيانات العقار مع المعلومات الفعلية
          _estate = _estate.copyWith(
            advertiserName: advertiserData['fullNameOrCompanyName'] ??
                           advertiserData['displayName'] ??
                           advertiserData['name'] ??
                           "غير محدد",
            advertiserImage: advertiserData['profileImageUrl'] ??
                           advertiserData['photoURL'],
            advertiserAdsCount: actualAdsCount,
            advertiserRegistrationDate: accountCreationDate,
            postedByUserType: advertiserData['userType'] ??
                            advertiserData['type'] ??
                            'seeker',
          );

          // التحقق من إمكانية إظهار رقم الهاتف
          _canShowPhone = (_advertiserPhoneNumber != null &&
                          _advertiserPhoneNumber!.isNotEmpty) &&
                         !widget.estate.hidePhone;

          _isLoadingAdvertiserData = false;
        });
      } else {
        setState(() {
          _isLoadingAdvertiserData = false;
          _canShowPhone = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading advertiser data: $e');
      setState(() {
        _isLoadingAdvertiserData = false;
        _canShowPhone = false;
      });
    }
  }

  // تحميل بيانات العقار من Firebase
  Future<void> _loadEstateData() async {
    try {
      // الحصول على بيانات العقار من Firestore
      final estateDoc = await FirebaseFirestore.instance
          .collection('estates')
          .doc(widget.estate.id)
          .get();

      if (estateDoc.exists) {
        final data = estateDoc.data() as Map<String, dynamic>;

        // تحديث العقار بالبيانات الكاملة
        setState(() {
          _estate = widget.estate.copyWith(
            // البيانات الأساسية
            title: data['title'] ?? widget.estate.title,
            description: data['description'] ?? widget.estate.description,
            price: (data['price'] ?? widget.estate.price).toDouble(),
            location: data['location'] ?? widget.estate.location,
            photoUrls: data['photoUrls'] != null
                ? List<String>.from(data['photoUrls'])
                : widget.estate.photoUrls,

            // بيانات المعلن
            advertiserName: data['advertiserName'] ?? widget.estate.advertiserName,
            advertiserImage: data['advertiserImage'] ?? widget.estate.advertiserImage,
            advertiserEmail: data['advertiserEmail'] ?? widget.estate.advertiserEmail,
            advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
                ? (data['advertiserRegistrationDate'] as Timestamp).toDate()
                : widget.estate.advertiserRegistrationDate,
            advertiserAdsCount: data['advertiserAdsCount'] ?? widget.estate.advertiserAdsCount,

            // خصائص الإعلان
            mainCategory: data['mainCategory'] ?? widget.estate.mainCategory,
            subCategory: data['subCategory'] ?? widget.estate.subCategory,
            propertyType: data['propertyType'] ?? widget.estate.propertyType,

            // خصائص العقار
            area: data['area'] != null ? (data['area'] as num).toDouble() : widget.estate.area,
            numberOfRooms: data['numberOfRooms'] ?? widget.estate.numberOfRooms,
            numberOfBathrooms: data['numberOfBathrooms'] ?? widget.estate.numberOfBathrooms,
            buildingAge: data['buildingAge'] ?? widget.estate.buildingAge,
            floorNumber: data['floorNumber'] ?? widget.estate.floorNumber,

            // المميزات الإضافية
            hasCentralAC: data['hasCentralAC'] ?? widget.estate.hasCentralAC,
            hasMaidRoom: data['hasMaidRoom'] ?? widget.estate.hasMaidRoom,
            hasGarage: data['hasGarage'] ?? widget.estate.hasGarage,
            hasSwimmingPool: data['hasSwimmingPool'] ?? widget.estate.hasSwimmingPool,
            hasElevator: data['hasElevator'] ?? widget.estate.hasElevator,
            isFullyFurnished: data['isFullyFurnished'] ?? widget.estate.isFullyFurnished,

            // الموقع
            lat: data['lat'] != null ? (data['lat'] as num).toDouble() : widget.estate.lat,
            lng: data['lng'] != null ? (data['lng'] as num).toDouble() : widget.estate.lng,

            // معلومات إضافية
            postedByUserType: data['postedByUserType'] ?? widget.estate.postedByUserType,
            isOriginal: data['isOriginal'] ?? widget.estate.isOriginal,
            originalEstateId: data['originalEstateId'] ?? widget.estate.originalEstateId);

          _isLoading = false;
        });
      } else {
        // إذا لم يوجد المستند، استخدم البيانات المتوفرة
        setState(() {
          _estate = widget.estate;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading estate data: $e');
      // في حالة حدوث خطأ، استخدم البيانات المتوفرة
      setState(() {
        _estate = widget.estate;
        _isLoading = false;
      });
    }
  }



  void _onScroll() {
    // تحديد متى يتم تغيير حالة الرأس (عند التمرير لأكثر من 120 بكسل)
    if (_scrollController.offset > 120 && !_isCollapsed) {
      setState(() {
        _isCollapsed = true;
      });
    } else if (_scrollController.offset <= 120 && _isCollapsed) {
      setState(() {
        _isCollapsed = false;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// دالة لتنسيق التاريخ إلى صيغة اليوم-الشهر-السنة.
  String _formatDate(DateTime? date) {
    if (date == null) return "غير محدد";
    return DateFormat('dd-MM-yyyy').format(date);
  }

  /// دالة مساعدة لبناء صف لعرض البيانات مع التسمية والقيمة.
  Widget _buildDetailRow(String label, String value,
      {TextStyle? labelStyle, TextStyle? valueStyle}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "$label: ",
          style: labelStyle ??
              GoogleFonts.cairo(fontSize: 14, fontWeight: FontWeight.bold)),
        Flexible(
          child: Text(
            value,
            style: valueStyle ?? GoogleFonts.cairo(fontSize: 14))),
      ]);
  }

  /// قسم بيانات المُعلِن مع تصميم عصري ومميز - يأخذ عرض الشاشة الكامل
  Widget _buildAdvertiserSection(Estate estate) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 16, left: 16, right: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.05),
            AppColors.primaryLight.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          // معلومات المعلن الرئيسية
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المُعلِن أو أول حرف من اسمه
              _buildAdvertiserAvatar(estate),
              const SizedBox(width: 16),

              // معلومات المُعلِن
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المُعلِن
                    Text(
                      estate.advertiserName ?? "غير محدد",
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // شارة نوع المستخدم
                    _buildModernUserTypeBadge(estate.postedByUserType),
                    const SizedBox(height: 12),

                    // عدد الإعلانات مع أيقونة - يظهر دائماً
                    _buildInfoRow(
                      Icons.campaign_outlined,
                      "عدد الإعلانات",
                      "${estate.advertiserAdsCount ?? 0}",
                      AppColors.primary,
                    ),
                    const SizedBox(height: 8),

                    // تاريخ التسجيل مع أيقونة - يظهر دائماً
                    _buildInfoRow(
                      Icons.calendar_today_outlined,
                      "تاريخ التسجيل",
                      estate.advertiserRegistrationDate != null
                          ? _formatDate(estate.advertiserRegistrationDate)
                          : "غير محدد",
                      AppColors.textSecondary,
                    ),
                    const SizedBox(height: 8),

                    // إذا كان العقار منسوخاً
                    if (!estate.isOriginal && estate.originalEstateId != null) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.content_copy, size: 16, color: Colors.amber[700]),
                            const SizedBox(width: 8),
                            Text(
                              "منسوخ من عقار أصلي",
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                color: Colors.amber[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
          // زر نسخ العقار للمستثمرين
          _buildCopyButton(estate),
        ],
      ),
    );
  }

  /// بناء صورة المعلن أو أول حرف من اسمه
  Widget _buildAdvertiserAvatar(Estate estate) {
    final String advertiserName = estate.advertiserName ?? "غير محدد";
    final String? advertiserImage = estate.advertiserImage;

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryLight,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: advertiserImage != null && advertiserImage.isNotEmpty
            ? Image.network(
                advertiserImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildInitialAvatar(advertiserName);
                },
              )
            : _buildInitialAvatar(advertiserName),
      ),
    );
  }

  /// بناء أفاتار بأول حرف من الاسم
  Widget _buildInitialAvatar(String name) {
    final String initial = name.isNotEmpty ? name[0].toUpperCase() : "؟";
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryLight,
          ],
        ),
      ),
      child: Center(
        child: Text(
          initial,
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// بناء شارة نوع المستخدم العصرية
  Widget _buildModernUserTypeBadge(String? userType) {
    if (userType == null || userType.isEmpty) {
      userType = 'seeker'; // افتراضي
    }

    final String displayName = UserTypeConstants.getArabicName(userType);
    Color badgeColor;
    IconData badgeIcon;
    List<Color> gradientColors;

    switch (userType) {
      case UserTypeConstants.agent:
        badgeColor = AppColors.primary;
        badgeIcon = Icons.business_center_outlined;
        gradientColors = [AppColors.primary, AppColors.primaryLight];
        break;
      case UserTypeConstants.owner:
        badgeColor = AppColors.secondary;
        badgeIcon = Icons.home_work_outlined;
        gradientColors = [AppColors.secondary, AppColors.secondaryLight];
        break;
      case UserTypeConstants.company:
        badgeColor = AppColors.primaryDark;
        badgeIcon = Icons.domain_outlined;
        gradientColors = [AppColors.primaryDark, AppColors.primary];
        break;
      case UserTypeConstants.seeker:
        badgeColor = AppColors.info;
        badgeIcon = Icons.search_outlined;
        gradientColors = [AppColors.info, AppColors.info.withValues(alpha: 0.7)];
        break;
      default:
        badgeColor = AppColors.textSecondary;
        badgeIcon = Icons.person_outline;
        gradientColors = [AppColors.textSecondary, AppColors.textLight];
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            badgeColor.withValues(alpha: 0.1),
            badgeColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: badgeColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: gradientColors),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              badgeIcon,
              size: 14,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            displayName,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: badgeColor,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات مع أيقونة
  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text(
          "$label: ",
          style: GoogleFonts.cairo(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }



  /// عرض الصور الخاصة بالإعلان بتصميم احترافي متقدم
  Widget _buildImageCarousel(Estate estate) {
    if (estate.photoUrls.isEmpty) {
      return Container(
        height: 300,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey.shade100,
              Colors.grey.shade200,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported_outlined,
                   size: 80, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                "لا توجد صور متاحة",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // إذا كان هناك صورة واحدة فقط
    if (estate.photoUrls.length == 1) {
      return _buildSingleImageView(estate);
    }

    // إذا كان هناك أكثر من صورة
    return _buildMultipleImagesView(estate);
  }

  /// عرض صورة واحدة بتصميم أنيق
  Widget _buildSingleImageView(Estate estate) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _isCollapsed ? 180 : 350,
      margin: EdgeInsets.symmetric(
        horizontal: _isCollapsed ? 24 : 16,
        vertical: 8,
      ),
      child: Hero(
        tag: 'estate_image_${estate.id}_0',
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(_isCollapsed ? 20 : 24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 40,
                offset: const Offset(0, 16),
                spreadRadius: 4,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(_isCollapsed ? 20 : 24),
            child: Stack(
              children: [
                // الصورة الرئيسية
                GestureDetector(
                  onTap: () => _showFullScreenImage(estate.photoUrls[0], 0),
                  child: Image.network(
                    estate.photoUrls[0],
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    loadingBuilder: _buildImageLoadingIndicator,
                    errorBuilder: _buildImageErrorWidget,
                  ),
                ),
                // تدرج في الأسفل للنص
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ),
                // أيقونة التكبير
                if (!_isCollapsed)
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.zoom_in,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// عرض متعدد الصور بتصميم احترافي
  Widget _buildMultipleImagesView(Estate estate) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _isCollapsed ? 180 : 350,
      margin: EdgeInsets.symmetric(
        horizontal: _isCollapsed ? 24 : 16,
        vertical: 8,
      ),
      child: Stack(
        children: [
          // عارض الصور الرئيسي
          PageView.builder(
            controller: _pageController,
            itemCount: estate.photoUrls.length,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return Hero(
                tag: 'estate_image_${estate.id}_$index',
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_isCollapsed ? 20 : 24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(_isCollapsed ? 20 : 24),
                    child: Stack(
                      children: [
                        // الصورة
                        GestureDetector(
                          onTap: () => _showFullScreenImage(estate.photoUrls[index], index),
                          child: Image.network(
                            estate.photoUrls[index],
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            loadingBuilder: _buildImageLoadingIndicator,
                            errorBuilder: _buildImageErrorWidget,
                          ),
                        ),
                        // تدرج في الأسفل
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withValues(alpha: 0.6),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // مؤشر الصفحات المحسن
          if (!_isCollapsed)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                        estate.photoUrls.length,
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          margin: const EdgeInsets.symmetric(horizontal: 3),
                          height: 6,
                          width: _currentImageIndex == index ? 20 : 6,
                          decoration: BoxDecoration(
                            color: _currentImageIndex == index
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // عداد الصور
          if (!_isCollapsed)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  "${_currentImageIndex + 1} / ${estate.photoUrls.length}",
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

          // أيقونة التكبير
          if (!_isCollapsed)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.zoom_in,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),

          // أزرار التنقل (للشاشات الكبيرة)
          if (!_isCollapsed && estate.photoUrls.length > 1)
            Positioned(
              left: 16,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_currentImageIndex > 0) {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.chevron_left,
                      color: _currentImageIndex > 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),

          if (!_isCollapsed && estate.photoUrls.length > 1)
            Positioned(
              right: 16,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_currentImageIndex < estate.photoUrls.length - 1) {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.chevron_right,
                      color: _currentImageIndex < estate.photoUrls.length - 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// عرض الصورة بشكل كامل مع إمكانية التكبير والتصغير
  void _showFullScreenImage(String imageUrl, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              "${index + 1} / ${_estate.photoUrls.length}",
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
          ),
          body: Center(
            child: InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.network(
                imageUrl,
                fit: BoxFit.contain,
                loadingBuilder: _buildImageLoadingIndicator,
                errorBuilder: _buildImageErrorWidget,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// مؤشر تحميل الصورة
  Widget _buildImageLoadingIndicator(BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
    if (loadingProgress == null) return child;
    return Center(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                  : null,
              color: Theme.of(context).primaryColor,
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            Text(
              "جاري تحميل الصورة...",
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// ويدجت خطأ تحميل الصورة
  Widget _buildImageErrorWidget(BuildContext context, Object error, StackTrace? stackTrace) {
    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image_outlined,
              size: 60,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              "فشل في تحميل الصورة",
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تم حذف الدالة المكررة

  /// عرض خريطة الموقع باستخدام إحداثيات العقار.
  Widget _buildMapSection(Estate estate) {
    final LatLng location = LatLng(estate.lat!, estate.lng!);
    return SizedBox(
      width: double.infinity,
      height: 200,
      child: GoogleMap(
        initialCameraPosition: CameraPosition(
          target: location,
          zoom: 14),
        markers: {
          Marker(
            markerId: const MarkerId("estate_location"),
            position: location)
        },
        onMapCreated: (GoogleMapController controller) {},
        myLocationButtonEnabled: false,
        zoomControlsEnabled: false,
        mapType: MapType.normal,
        compassEnabled: false,
        rotateGesturesEnabled: false,
        scrollGesturesEnabled: true,
        zoomGesturesEnabled: true,
        tiltGesturesEnabled: false,
      ),
    );
  }

  /// عرض المميزات العقارية بتصميم متدفق وأنيق
  Widget _buildPropertyFeatures(Estate estate) {
    // قائمة المميزات المتاحة مع الأيقونات والألوان
    final List<Map<String, dynamic>> featuresData = [];

    // فحص جميع المميزات المتاحة في العقار وإضافة المتوفرة منها فقط

    // إضافة المميزات المتوفرة فقط مع ألوان مخصصة
    if (estate.hasCentralAC) {
      featuresData.add({
        'icon': Icons.ac_unit,
        'text': 'تكييف مركزي',
        'color': AppColors.primary
      });
    }

    if (estate.hasSecurity != null && estate.hasSecurity!) {
      featuresData.add({
        'icon': Icons.security,
        'text': 'أمن وحماية',
        'color': AppColors.warning
      });
    }

    if (estate.allowPets != null && estate.allowPets!) {
      featuresData.add({
        'icon': Icons.pets,
        'text': 'السماح بالحيوانات الأليفة',
        'color': AppColors.secondary
      });
    }

    if (estate.hasElevator != null && estate.hasElevator!) {
      featuresData.add({
        'icon': Icons.elevator,
        'text': 'مصعد',
        'color': AppColors.info
      });
    }

    if (estate.hasSwimmingPool != null && estate.hasSwimmingPool!) {
      featuresData.add({
        'icon': Icons.pool,
        'text': 'مسبح',
        'color': AppColors.primaryLight
      });
    }

    if (estate.hasMaidRoom) {
      featuresData.add({
        'icon': Icons.bedroom_child,
        'text': 'غرفة خادمة',
        'color': AppColors.secondary
      });
    }

    if (estate.hasGarage) {
      featuresData.add({
        'icon': Icons.garage,
        'text': 'مرآب/كراج',
        'color': AppColors.primaryDark
      });
    }

    if (estate.hasBalcony != null && estate.hasBalcony!) {
      featuresData.add({
        'icon': Icons.balcony,
        'text': 'شرفة',
        'color': AppColors.success
      });
    }

    if (estate.isFullyFurnished != null && estate.isFullyFurnished!) {
      featuresData.add({
        'icon': Icons.chair,
        'text': 'مفروش بالكامل',
        'color': AppColors.info
      });
    }

    // المميزات الإضافية الجديدة
    if (estate.hasGarden != null && estate.hasGarden!) {
      featuresData.add({
        'icon': Icons.local_florist,
        'text': 'حديقة',
        'color': AppColors.success
      });
    }

    if (estate.hasPool != null && estate.hasPool!) {
      featuresData.add({
        'icon': Icons.pool_outlined,
        'text': 'مسبح خاص',
        'color': AppColors.primary
      });
    }

    if (estate.hasDriverRoom != null && estate.hasDriverRoom!) {
      featuresData.add({
        'icon': Icons.drive_eta,
        'text': 'غرفة سائق',
        'color': AppColors.primaryLight
      });
    }

    if (estate.hasPrivateEntrance != null && estate.hasPrivateEntrance!) {
      featuresData.add({
        'icon': Icons.door_front_door,
        'text': 'مدخل خاص',
        'color': AppColors.secondary
      });
    }

    if (estate.hasEquippedKitchen != null && estate.hasEquippedKitchen!) {
      featuresData.add({
        'icon': Icons.kitchen,
        'text': 'مطبخ مجهز',
        'color': AppColors.primaryDark
      });
    }

    // إذا لم تكن هناك ميزات، نعرض رسالة
    if (featuresData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 12),
            Text(
              "لا توجد مميزات إضافية مسجلة لهذا العقار",
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: featuresData.map((feature) {
        return _buildFlowingFeatureChip(
          feature['icon'] as IconData,
          feature['text'] as String,
          feature['color'] as Color,
        );
      }).toList(),
    );
  }

  /// بناء عنصر ميزة متدفق بتصميم أنيق
  Widget _buildFlowingFeatureChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }





  /// بناء زر نسخ العقار للوكلاء العقاريين والمستثمرين
  Widget _buildCopyButton(Estate estate) {
    // الحصول على معرف المستخدم الحالي
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;

    // التحقق من نوع المستخدم الحالي
    return FutureBuilder<DocumentSnapshot>(
      future: FirebaseFirestore.instance
          .collection('users')
          .doc(currentUserId)
          .get(),
      builder: (context, snapshot) {
        // إذا لم تكتمل عملية جلب البيانات بعد
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        // الحصول على نوع المستخدم
        final userData = snapshot.data!.data() as Map<String, dynamic>?;
        final userType = userData?['userType'] as String? ?? 'user';

        // لا نعرض الزر في الحالات التالية:
        // 1. إذا لم يكن المستخدم مسجل دخول
        // 2. إذا كان العقار ليس أصلياً (نسخة)
        // 3. إذا كان المستخدم هو مالك العقار
        // 4. إذا كان المستخدم قد نسخ العقار بالفعل
        // 5. إذا لم يكن المستخدم من نوع "وكيل عقاري" أو "مستثمر"
        // 6. إذا لم يكن العقار للإيجار
        if (currentUserId == null ||
            !estate.isOriginal ||
            currentUserId == estate.ownerId ||
            estate.copiedBy.contains(currentUserId) ||
            (userType != 'investor' && userType != 'agent') ||
            estate.mainCategory != 'عقار للايجار') {
          return const SizedBox.shrink();
        }

        return ElevatedButton.icon(
          icon: const Icon(Icons.copy, size: 16),
          label: const Text('نسخ العقار', style: TextStyle(fontSize: 12)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.amber,
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            minimumSize: const Size(50, 24),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap),
          onPressed: () {
            _showCopyConfirmationDialog(context, estate.id, currentUserId);
          });
      });
  }

  /// عرض مربع حوار تأكيد نسخ العقار
  void _showCopyConfirmationDialog(
      BuildContext context, String estateId, String investorId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.content_copy, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'نسخ العقار',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من رغبتك في نسخ هذا العقار إلى حسابك؟',
              style: GoogleFonts.cairo(fontSize: 14)),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملاحظة:',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700)),
                  const SizedBox(height: 4),
                  Text(
                    '• سيظهر العقار في قائمة عقاراتك مع الإشارة إلى المالك الأصلي\n'
                    '• يمكنك تعديل السعر وإضافة ملاحظاتك الخاصة\n'
                    '• ستحصل على عمولة عند إتمام الصفقة',
                    style: GoogleFonts.cairo(
                      fontSize: 11,
                      color: Colors.blue.shade600)),
                ])),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.grey.shade600))),
          ElevatedButton(
            onPressed: () {
              // إرسال حدث نسخ العقار
              BlocProvider.of<EstateBloc>(context)
                  .add(CopyEstateEvent(estateId, investorId));
              Navigator.pop(context);

              // عرض رسالة نجاح محسنة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'تم نسخ العقار بنجاح وإضافته إلى قائمة عقاراتك',
                          style: GoogleFonts.cairo(color: Colors.white))),
                    ]),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
                  action: SnackBarAction(
                    label: 'عرض',
                    textColor: Colors.white,
                    onPressed: () {
                      // الانتقال إلى صفحة عقاراتي
                      Navigator.pushNamed(context, '/my-properties');
                    })));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))),
            child: Text(
              'نسخ العقار',
              style: GoogleFonts.cairo(color: Colors.white))),
        ]));
  }

  /// عرض مربع حوار طلب عقار للباحثين
  void _showPropertyRequestDialog(BuildContext context, Estate estate) {
    final TextEditingController messageController = TextEditingController();
    final TextEditingController budgetController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.request_page, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'طلب عقار مشابه',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أنشئ طلب للحصول على عقارات مشابهة لهذا العقار',
                style: GoogleFonts.cairo(fontSize: 14)),
              const SizedBox(height: 16),
              TextField(
                controller: budgetController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الميزانية المتاحة (د.ك)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.attach_money))),
              const SizedBox(height: 12),
              TextField(
                controller: messageController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'متطلبات إضافية (اختياري)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.message),
                  hintText: 'مثال: أفضل الدور الأرضي، قريب من المدارس...')),
            ])),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.grey.shade600))),
          ElevatedButton(
            onPressed: () {
              if (budgetController.text.isNotEmpty) {
                // إنشاء طلب العقار
                _createPropertyRequest(estate, budgetController.text, messageController.text);
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تم إنشاء طلب العقار بنجاح. سيتم إشعارك عند توفر عقارات مشابهة',
                            style: GoogleFonts.cairo(color: Colors.white))),
                      ]),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))));
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))),
            child: Text(
              'إنشاء الطلب',
              style: GoogleFonts.cairo(color: Colors.white))),
        ]));
  }

  /// إنشاء طلب عقار
  Future<void> _createPropertyRequest(Estate estate, String budget, String message) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // الحصول على بيانات المستخدم
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data() ?? {};
      final userName = userData['fullName'] ?? user.displayName ?? 'مستخدم';
      final userImage = userData['profileImage'] ?? user.photoURL;

      // إنشاء طلب العقار باستخدام النموذج المتقدم
      final request = PropertyRequestModel(
        id: '', // سيتم تعيينه في الخدمة
        title: 'طلب عقار مشابه لـ ${estate.title}',
        description: message.isNotEmpty ? message : 'أبحث عن عقار مشابه لهذا العقار',
        userId: user.uid,
        userName: userName,
        userImage: userImage,
        propertyType: estate.propertyType ?? 'شقة',
        minPrice: (double.tryParse(budget) ?? 0) * 0.8, // 20% أقل من الميزانية
        maxPrice: double.tryParse(budget) ?? 0,
        preferredLocations: [estate.location],
        minRooms: estate.numberOfRooms,
        minBathrooms: estate.numberOfBathrooms,
        minArea: estate.area,
        hasCentralAC: estate.hasCentralAC,
        hasMaidRoom: estate.hasMaidRoom,
        hasGarage: estate.hasGarage,
        hasSwimmingPool: estate.hasSwimmingPool ?? false,
        hasElevator: estate.hasElevator ?? false,
        isFullyFurnished: estate.isFullyFurnished ?? false,
        additionalRequirements: 'مرجع العقار: ${estate.id}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      // استخدام الخدمة لإنشاء الطلب
      final propertyRequestService = PropertyRequestService(
        repository: PropertyRequestRepositoryImpl(
          firestore: FirebaseFirestore.instance,
          storage: FirebaseStorage.instance),
        messagingService: MessagingService(
          messagingRepository: MessagingRepositoryImpl()));

      await propertyRequestService.createPropertyRequest(request);
    } catch (e) {
      debugPrint('Error creating property request: $e');
    }
  }

  /// بناء قسم الأيقونات للتواصل بعد الوصف.
  /// إذا كانت خاصية إخفاء الهاتف false تظهر أيقونتا الهاتف والواتساب مع الوظائف،
  /// وإلا يتم إخفاؤهما ويظل زر المراسلة ظاهرًا بدون وظيفة.
  Widget _buildContactIcons(Estate estate) {
    final bool hidePhone = estate.hidePhone;
    String phoneNumber = "";
    if (!hidePhone && estate.extraPhones.isNotEmpty) {
      phoneNumber = estate.extraPhones.first;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (!hidePhone && phoneNumber.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.phone, color: Colors.green),
              onPressed: () async {
                final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
                if (await canLaunchUrl(launchUri)) {
                  await launchUrl(launchUri);
                }
              }),
          if (!hidePhone && phoneNumber.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.phone_android, color: Colors.green),
              onPressed: () async {
                final whatsappUrl = "https://wa.me/$phoneNumber";
                final Uri whatsappUri = Uri.parse(whatsappUrl);
                if (await canLaunchUrl(whatsappUri)) {
                  await launchUrl(whatsappUri);
                }
              }),
          // أيقونة المراسلة تظهر دائماً بدون وظيفة
          IconButton(
            icon: const Icon(Icons.message, color: Colors.green),
            onPressed: () {
              // لا توجد وظيفة حالياً
            }),
        ]));
  }

  /// بناء قسم المميزات الإضافية للعقار بشكل شامل
  Widget _buildAdditionalFeatures(Estate estate) {
    final List<Map<String, dynamic>> features = [];

    // المميزات الأساسية
    if (estate.hasCentralAC) {
      features.add({
        'icon': Icons.ac_unit_outlined,
        'text': 'تكييف مركزي',
        'color': AppColors.primary,
      });
    }

    if (estate.hasMaidRoom) {
      features.add({
        'icon': Icons.bedroom_child_outlined,
        'text': 'غرفة خادمة',
        'color': AppColors.secondary,
      });
    }

    if (estate.hasGarage) {
      features.add({
        'icon': Icons.garage_outlined,
        'text': 'كراج',
        'color': AppColors.info,
      });
    }

    if (estate.hasSwimmingPool == true) {
      features.add({
        'icon': Icons.pool_outlined,
        'text': 'مسبح',
        'color': AppColors.primaryLight,
      });
    }

    if (estate.hasElevator == true) {
      features.add({
        'icon': Icons.elevator_outlined,
        'text': 'مصعد',
        'color': AppColors.primaryDark,
      });
    }

    if (estate.isFullyFurnished == true) {
      features.add({
        'icon': Icons.chair_outlined,
        'text': 'مفروش بالكامل',
        'color': AppColors.success,
      });
    }

    // المميزات الإضافية المتاحة في النموذج
    if (estate.hasSecurity == true) {
      features.add({
        'icon': Icons.security_outlined,
        'text': 'حراسة أمنية',
        'color': AppColors.warning,
      });
    }

    if (estate.allowPets == true) {
      features.add({
        'icon': Icons.pets_outlined,
        'text': 'يسمح بالحيوانات',
        'color': AppColors.info,
      });
    }

    if (estate.hasBalcony == true) {
      features.add({
        'icon': Icons.balcony_outlined,
        'text': 'شرفة',
        'color': AppColors.primaryLight,
      });
    }

    // المميزات الإضافية من الحقول المتاحة
    if (estate.hasGarden == true) {
      features.add({
        'icon': Icons.local_florist_outlined,
        'text': 'حديقة',
        'color': AppColors.success,
      });
    }

    if (estate.hasPool == true) {
      features.add({
        'icon': Icons.pool_outlined,
        'text': 'مسبح',
        'color': AppColors.primary,
      });
    }

    if (estate.hasDriverRoom == true) {
      features.add({
        'icon': Icons.drive_eta_outlined,
        'text': 'غرفة سائق',
        'color': AppColors.secondary,
      });
    }

    if (estate.hasPrivateEntrance == true) {
      features.add({
        'icon': Icons.door_front_door_outlined,
        'text': 'مدخل خاص',
        'color': AppColors.info,
      });
    }

    if (estate.hasEquippedKitchen == true) {
      features.add({
        'icon': Icons.kitchen_outlined,
        'text': 'مطبخ مجهز',
        'color': AppColors.primaryDark,
      });
    }

    // إذا لم تكن هناك مميزات
    if (features.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              "لا توجد مميزات إضافية محددة",
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Text(
          "المميزات الإضافية",
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),

        // شبكة المميزات
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: features.map((feature) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    feature['color'].withValues(alpha: 0.1),
                    feature['color'].withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: feature['color'].withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: feature['color'],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      feature['icon'],
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    feature['text'],
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: feature['color'],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// بناء زر تواصل
  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.bold)),
          ])));
  }

  /// بناء قسم أزرار التواصل المحسنة
  Widget _buildEnhancedContactButtons(Estate estate) {
    final bool hidePhone = estate.hidePhone;
    String phoneNumber = "";
    if (!hidePhone && estate.extraPhones.isNotEmpty) {
      phoneNumber = estate.extraPhones.first;
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2)),
        ]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (!hidePhone && phoneNumber.isNotEmpty)
            _buildContactButton(
              icon: Icons.phone,
              label: "اتصال",
              color: Colors.green,
              onTap: () async {
                final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
                if (await canLaunchUrl(launchUri)) {
                  await launchUrl(launchUri);
                }
              }),
          if (!hidePhone && phoneNumber.isNotEmpty)
            _buildContactButton(
              icon: Icons.message,
              label: "واتساب",
              color: Colors.green.shade700,
              onTap: () async {
                final whatsappUrl = "https://wa.me/$phoneNumber";
                final Uri whatsappUri = Uri.parse(whatsappUrl);
                if (await canLaunchUrl(whatsappUri)) {
                  await launchUrl(whatsappUri);
                }
              }),
          _buildContactButton(
            icon: Icons.mail,
            label: "مراسلة",
            color: Colors.blue,
            onTap: () {
              // TODO: Implement messaging functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text("سيتم تفعيل خاصية المراسلة قريباً")));
            }),
        ]));
  }

  // تم حذف دوال الأزرار المخصصة لتبسيط الواجهة

  // ===== وظائف خاصة بالباحث عن عقار =====

  /// عرض حاسبة التمويل العقاري
  void _showFinanceCalculatorDialog(BuildContext context, Estate estate) {
    final TextEditingController downPaymentController = TextEditingController();
    final TextEditingController interestRateController = TextEditingController(text: '3.5');
    final TextEditingController yearsController = TextEditingController(text: '20');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.calculate, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'حاسبة التمويل العقاري',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'سعر العقار: ${estate.price} د.ك',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor)),
              const SizedBox(height: 16),
              TextField(
                controller: downPaymentController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الدفعة المقدمة (د.ك)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.attach_money))),
              const SizedBox(height: 12),
              TextField(
                controller: interestRateController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'معدل الفائدة السنوي (%)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.percent))),
              const SizedBox(height: 12),
              TextField(
                controller: yearsController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'مدة التمويل (سنة)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.calendar_today))),
            ])),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () {
              _calculateFinancing(
                estate.price,
                double.tryParse(downPaymentController.text) ?? 0,
                double.tryParse(interestRateController.text) ?? 3.5,
                int.tryParse(yearsController.text) ?? 20);
              Navigator.pop(context);
            },
            child: Text('احسب', style: GoogleFonts.cairo())),
        ]));
  }

  /// حساب التمويل العقاري
  void _calculateFinancing(double propertyPrice, double downPayment, double interestRate, int years) {
    final loanAmount = propertyPrice - downPayment;
    final monthlyRate = interestRate / 100 / 12;
    final numberOfPayments = years * 12;

    final monthlyPayment = loanAmount *
        (monthlyRate * pow(1 + monthlyRate, numberOfPayments)) /
        (pow(1 + monthlyRate, numberOfPayments) - 1);

    final totalPayment = monthlyPayment * numberOfPayments;
    final totalInterest = totalPayment - loanAmount;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'نتائج حاسبة التمويل',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCalculationRow('مبلغ القرض:', '${loanAmount.toStringAsFixed(2)} د.ك'),
            _buildCalculationRow('القسط الشهري:', '${monthlyPayment.toStringAsFixed(2)} د.ك'),
            _buildCalculationRow('إجمالي المدفوعات:', '${totalPayment.toStringAsFixed(2)} د.ك'),
            _buildCalculationRow('إجمالي الفوائد:', '${totalInterest.toStringAsFixed(2)} د.ك'),
          ]),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: GoogleFonts.cairo())),
        ]));
  }

  Widget _buildCalculationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo(fontSize: 14)),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor)),
        ]));
  }

  /// إضافة العقار للمقارنة
  void _addToComparison(Estate estate) {
    // TODO: Implement comparison functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم إضافة العقار للمقارنة',
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))));
  }

  // ===== وظائف خاصة بالمستثمر =====

  /// عرض تحليل الاستثمار
  void _showInvestmentAnalysisDialog(BuildContext context, Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.analytics, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'تحليل الاستثمار',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAnalysisCard(
                'العائد المتوقع سنوياً',
                '${(estate.price * 0.08).toStringAsFixed(0)} د.ك',
                '8% من قيمة العقار',
                Colors.green),
              const SizedBox(height: 12),
              _buildAnalysisCard(
                'فترة الاسترداد المتوقعة',
                '12.5 سنة',
                'بناءً على العائد الحالي',
                Colors.blue),
              const SizedBox(height: 12),
              _buildAnalysisCard(
                'تقدير نمو القيمة',
                '${(estate.price * 1.05).toStringAsFixed(0)} د.ك',
                '5% نمو سنوي متوقع',
                Colors.orange),
            ])),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: GoogleFonts.cairo())),
        ]));
  }

  Widget _buildAnalysisCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color)),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold)),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600)),
        ]));
  }

  /// عرض توقعات السوق
  void _showMarketTrendsDialog(BuildContext context, Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.trending_up, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'توقعات السوق',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'منطقة: ${estate.location}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            _buildTrendItem('اتجاه الأسعار', 'صاعد', Colors.green, Icons.trending_up),
            _buildTrendItem('الطلب', 'مرتفع', Colors.blue, Icons.thumb_up),
            _buildTrendItem('المعروض', 'متوسط', Colors.orange, Icons.inventory),
            _buildTrendItem('التوقع للعام القادم', 'إيجابي', Colors.green, Icons.star),
          ]),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: GoogleFonts.cairo())),
        ]));
  }

  Widget _buildTrendItem(String label, String value, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(label, style: GoogleFonts.cairo(fontSize: 14))),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12)),
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.bold))),
        ]));
  }

  // ===== وظائف خاصة بمالك العقار =====

  /// تعديل العقار
  void _editEstate(Estate estate) {
    // TODO: Navigate to edit estate page
    Navigator.pushNamed(context, '/edit-estate', arguments: estate);
  }

  /// عرض إحصائيات المشاهدة
  void _showViewStatistics(BuildContext context, Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.visibility, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'إحصائيات المشاهدة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatisticItem('إجمالي المشاهدات', '1,234', Icons.visibility),
            _buildStatisticItem('مشاهدات اليوم', '45', Icons.today),
            _buildStatisticItem('مشاهدات هذا الأسبوع', '312', Icons.date_range),
            _buildStatisticItem('الاستفسارات', '23', Icons.question_answer),
            _buildStatisticItem('المفضلة', '67', Icons.favorite),
          ]),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: GoogleFonts.cairo())),
        ]));
  }

  Widget _buildStatisticItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(label, style: GoogleFonts.cairo(fontSize: 14))),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor)),
        ]));
  }

  /// ترويج العقار
  void _promoteEstate(Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.campaign, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'ترويج الإعلان',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر نوع الترويج المناسب لإعلانك',
              style: GoogleFonts.cairo(fontSize: 14)),
            const SizedBox(height: 16),
            _buildPromotionOption('ترويج عادي', '5 د.ك', 'لمدة 7 أيام'),
            _buildPromotionOption('ترويج مميز', '15 د.ك', 'لمدة 15 يوم'),
            _buildPromotionOption('ترويج ذهبي', '30 د.ك', 'لمدة 30 يوم'),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo())),
        ]));
  }

  Widget _buildPromotionOption(String title, String price, String duration) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        // TODO: Navigate to payment page
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('سيتم توجيهك لصفحة الدفع', style: GoogleFonts.cairo()),
            backgroundColor: Colors.blue));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold)),
                Text(
                  duration,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600)),
              ]),
            Text(
              price,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor)),
          ])));
  }

  // ===== وظائف خاصة بالشركات العقارية =====

  /// إضافة لحملة
  void _addToCampaign(Estate estate) {
    // TODO: Implement campaign functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم إضافة العقار للحملة الترويجية',
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))));
  }

  /// إنشاء تقرير مفصل
  void _generateDetailedReport(Estate estate) {
    // TODO: Implement detailed report functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'جاري إنشاء التقرير المفصل...',
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))));
  }

  // ===== وظائف عامة =====

  /// عرض معلومات إضافية
  void _showAdditionalInfo(BuildContext context, Estate estate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.info, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'معلومات إضافية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold)),
          ]),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معرف الإعلان: ${estate.id}',
              style: GoogleFonts.cairo(fontSize: 14)),
            const SizedBox(height: 8),
            Text(
              'تاريخ النشر: ${_formatDate(estate.createdAt)}',
              style: GoogleFonts.cairo(fontSize: 14)),
            const SizedBox(height: 8),
            Text(
              'آخر تحديث: ${_formatDate(estate.updatedAt)}',
              style: GoogleFonts.cairo(fontSize: 14)),
            if (estate.mainCategory != null) ...[
              const SizedBox(height: 8),
              Text(
                'الفئة الرئيسية: ${estate.mainCategory}',
                style: GoogleFonts.cairo(fontSize: 14)),
            ],
            if (estate.subCategory != null) ...[
              const SizedBox(height: 8),
              Text(
                'الفئة الفرعية: ${estate.subCategory}',
                style: GoogleFonts.cairo(fontSize: 14)),
            ],
          ]),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: GoogleFonts.cairo())),
        ]));
  }

  @override
  Widget build(BuildContext context) {
    // حساب ارتفاع الشاشة وعرضها
    final screenHeight = MediaQuery.of(context).size.height;

    // تحديد ألوان التصميم
    final primaryColor = Theme.of(context).primaryColor;
    final secondaryColor = Colors.amber;
    final backgroundColor = Colors.grey.shade50;
    final textColor = Colors.grey.shade800;

    // ارتفاع الصورة في الحالة الكاملة
    final double expandedImageHeight = screenHeight * 0.45;

    // Mostrar pantalla de carga mientras se obtienen los datos
    if (_isLoading) {
      return Scaffold(
        backgroundColor: backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(200),
                shape: BoxShape.circle),
              child: Icon(Icons.arrow_back_ios_new, color: primaryColor, size: 18)),
            onPressed: () => Navigator.pop(context))),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: primaryColor),
              const SizedBox(height: 16),
              Text(
                "جاري تحميل بيانات العقار...",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: textColor)),
            ])));
    }

    return Scaffold(
      backgroundColor: backgroundColor,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // المحتوى القابل للتمرير
          CustomScrollView(
            controller: _scrollController,
            slivers: [
              // الجزء العلوي المتغير (الصورة والمعلومات الأساسية)
              SliverAppBar(
                expandedHeight: expandedImageHeight,
                floating: false,
                pinned: true,
                backgroundColor: backgroundColor,
                elevation: 0,
                flexibleSpace: LayoutBuilder(
                  builder: (context, constraints) {
                    // حساب نسبة التمدد (1.0 = ممتد بالكامل، 0.0 = مطوي)
                    final expandRatio =
                        (constraints.maxHeight - kToolbarHeight) /
                            (expandedImageHeight - kToolbarHeight);
                    final isCollapsed = expandRatio < 0.5;

                    return FlexibleSpaceBar(
                      background: Stack(
                        children: [
                          // صور العقار
                          SizedBox(
                            height: expandedImageHeight,
                            width: double.infinity,
                            child: Stack(
                              children: [
                                // عرض الصور
                                PageView.builder(
                                  controller: _pageController,
                                  itemCount: _estate.photoUrls.isEmpty
                                      ? 1
                                      : _estate.photoUrls.length,
                                  onPageChanged: (index) {
                                    setState(() {
                                      _currentImageIndex = index;
                                    });
                                  },
                                  itemBuilder: (context, index) {
                                    return _estate.photoUrls.isNotEmpty
                                        ? Hero(
                                            tag:
                                                'estate_image_${_estate.id}_$index',
                                            child: Image.network(
                                              _estate.photoUrls[index],
                                              fit: BoxFit.cover,
                                              loadingBuilder: (context, child,
                                                  loadingProgress) {
                                                if (loadingProgress == null) {
                                                  return child;
                                                }
                                                return Container(
                                                  color: Colors.grey.shade200,
                                                  child: Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      value: loadingProgress
                                                                  .expectedTotalBytes !=
                                                              null
                                                          ? loadingProgress
                                                                  .cumulativeBytesLoaded /
                                                              loadingProgress
                                                                  .expectedTotalBytes!
                                                          : null,
                                                      color: primaryColor)));
                                              },
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  color: Colors.grey.shade200,
                                                  child: Center(
                                                    child: Icon(
                                                      Icons
                                                          .broken_image_rounded,
                                                      size: 60,
                                                      color:
                                                          Colors.grey.shade400)));
                                              }))
                                        : Container(
                                            color: Colors.grey.shade200,
                                            child: Center(
                                              child: Icon(
                                                Icons.home_outlined,
                                                size: 80,
                                                color: Colors.grey.shade400)));
                                  }),

                                // طبقة تدرج شفافة في أسفل الصورة
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: Container(
                                    height: 120,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.transparent,
                                          Colors.black.withAlpha(180),
                                        ])))),

                                // معلومات العقار الأساسية فوق الطبقة الشفافة
                                Positioned(
                                  bottom: 20,
                                  left: 20,
                                  right: 20,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // السعر
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        decoration: BoxDecoration(
                                          color: secondaryColor,
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                        child: Text(
                                          "${_estate.price} د.ك",
                                          style: GoogleFonts.cairo(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white))),
                                      const SizedBox(height: 8),
                                      // العنوان
                                      Text(
                                        _estate.title,
                                        style: GoogleFonts.cairo(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          shadows: [
                                            Shadow(
                                              color:
                                                  Colors.black.withAlpha(128),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2)),
                                          ]),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis),
                                      const SizedBox(height: 4),
                                      // الموقع
                                      Row(
                                        children: [
                                          Icon(Icons.location_on,
                                              color: Colors.white, size: 16),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              _estate.location,
                                              style: GoogleFonts.cairo(
                                                fontSize: 14,
                                                color: Colors.white,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withAlpha(128),
                                                    blurRadius: 4,
                                                    offset: const Offset(0, 2)),
                                                ]),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis)),
                                        ]),
                                    ])),



                                // مؤشرات الصور (النقاط)
                                if (_estate.photoUrls.length > 1)
                                  Positioned(
                                    bottom: 100,
                                    left: 0,
                                    right: 0,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: List.generate(
                                        _estate.photoUrls.length,
                                        (index) => GestureDetector(
                                          onTap: () {
                                            _pageController.animateToPage(
                                              index,
                                              duration: const Duration(
                                                  milliseconds: 300),
                                              curve: Curves.easeInOut);
                                          },
                                          child: Container(
                                            width: 8,
                                            height: 8,
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 4),
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: index == _currentImageIndex
                                                  ? Colors.white
                                                  : Colors.white.withAlpha(120))))))),
                              ])),
                        ]),
                      title: isCollapsed
                          ? Row(
                              children: [
                                // صورة مصغرة عند التمرير
                                if (_estate.photoUrls.isNotEmpty)
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      _estate.photoUrls.first,
                                      width: 40,
                                      height: 40,
                                      fit: BoxFit.cover)),
                                const SizedBox(width: 12),
                                // عنوان مصغر
                                Expanded(
                                  child: Text(
                                    _estate.title,
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: textColor),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis)),
                              ])
                          : null);
                  }),
                leading: IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(200),
                      shape: BoxShape.circle),
                    child: Icon(Icons.arrow_back_ios_new,
                        color: primaryColor, size: 18)),
                  onPressed: () => Navigator.pop(context)),
                actions: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(200),
                        shape: BoxShape.circle),
                      child: Icon(Icons.share, color: primaryColor, size: 18)),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('سيتم إضافة ميزة المشاركة قريباً')));
                    }),
                  const SizedBox(width: 8),
                ]),

              // المحتوى الرئيسي
              SliverToBoxAdapter(
                child: Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24))),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات المُعلِن بالتصميم الجديد - الأولى بعد الصور
                      _buildAdvertiserSection(_estate),

                      // المميزات الرئيسية للعقار بتصميم متدفق
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 16, left: 16, right: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(8),
                              blurRadius: 12,
                              offset: const Offset(0, 4)),
                          ]),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "المعلومات الأساسية",
                              style: GoogleFonts.cairo(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: primaryColor)),
                            const SizedBox(height: 16),
                            _buildMainPropertyInfo(_estate),
                          ])),

                      // وصف العقار بتصميم محسن
                      Container(
                        width: double.infinity,
                        margin:
                            const EdgeInsets.only(top: 24, left: 16, right: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(8),
                              blurRadius: 12,
                              offset: const Offset(0, 4)),
                          ]),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.description,
                                     color: primaryColor, size: 22),
                                const SizedBox(width: 8),
                                Text(
                                  "وصف العقار",
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: primaryColor)),
                              ]),
                            const SizedBox(height: 16),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.grey.shade200,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                widget.estate.description,
                                style: GoogleFonts.cairo(
                                  fontSize: 15,
                                  height: 1.6,
                                  color: textColor),
                                textAlign: TextAlign.justify)),
                          ])),



                      // تفاصيل العقار الداخلية بتصميم متدفق
                      Container(
                        width: double.infinity,
                        margin:
                            const EdgeInsets.only(top: 24, left: 16, right: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(8),
                              blurRadius: 12,
                              offset: const Offset(0, 4)),
                          ]),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "مميزات العقار",
                              style: GoogleFonts.cairo(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: primaryColor)),
                            const SizedBox(height: 16),
                            _buildPropertyFeatures(widget.estate),
                          ])),

                      // الموقع على الخريطة
                      if (widget.estate.shareLocation &&
                          widget.estate.lat != null &&
                          widget.estate.lng != null)
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(
                              top: 24, left: 16, right: 16),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(13),
                                blurRadius: 10,
                                offset: const Offset(0, 2)),
                            ]),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "الموقع على الخريطة",
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: primaryColor)),
                              const SizedBox(height: 12),
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: SizedBox(
                                  width: double.infinity,
                                  height: 200,
                                  child: _buildMapSection(widget.estate))),
                            ])),

                      // تم حذف الأزرار المخصصة لتبسيط الواجهة

                      // قسم التقييمات والمراجعات المحدث
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 24, left: 16, right: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.primary.withValues(alpha: 0.03),
                              AppColors.primaryLight.withValues(alpha: 0.01),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withValues(alpha: 0.08),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // عنوان القسم مع أيقونة
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [AppColors.primary, AppColors.primaryLight],
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Icon(
                                    Icons.star_rate,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  "التقييمات والمراجعات",
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // ويدجت التقييمات
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: AppColors.border,
                                  width: 1,
                                ),
                              ),
                              child: RatingWidget(
                                itemId: _estate.id,
                                itemType: RatableItemType.estate,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // قسم الجولة الافتراضية
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 24, left: 16, right: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(13),
                              blurRadius: 10,
                              offset: const Offset(0, 2)),
                          ]),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "الجولة الافتراضية",
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor)),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.pushNamed(
                                      context,
                                      AppRoutes.virtualTourViewer,
                                      arguments: {'estateId': _estate.id},
                                    );
                                  },
                                  icon: const Icon(Icons.view_in_ar),
                                  label: Text('عرض كامل', style: GoogleFonts.cairo()),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ]),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: VirtualTourViewer(
                                estateId: _estate.id,
                                height: 200,
                              ),
                            ),
                          ])),

                      // أزرار التواصل
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(
                            vertical: 24, horizontal: 16),
                        child: Row(
                          children: [
                            // زر الاتصال - يظهر فقط إذا كان رقم الهاتف متاح
                            if (_canShowPhone || _isLoadingAdvertiserData)
                              Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Theme.of(context).primaryColor,
                                      Theme.of(context)
                                          .primaryColor
                                          .withAlpha(180),
                                    ])),
                                child: ElevatedButton.icon(
                                  icon: _isLoadingAdvertiserData
                                      ? const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                                      : const Icon(Icons.phone),
                                  label: Text(_isLoadingAdvertiserData ? "تحميل..." : "اتصال"),
                                  style: ElevatedButton.styleFrom(
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    backgroundColor: Colors.transparent,
                                    shadowColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                                  onPressed: () async {
                                    if (_isLoadingAdvertiserData) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                            content: Text('جاري تحميل البيانات...')));
                                      return;
                                    }

                                    if (_canShowPhone && _advertiserPhoneNumber != null) {
                                      final Uri launchUri =
                                          Uri(scheme: 'tel', path: _advertiserPhoneNumber!);
                                      if (await canLaunchUrl(launchUri)) {
                                        await launchUrl(launchUri);
                                      }
                                    } else {
                                      String message = 'رقم الهاتف غير متاح';
                                      if (widget.estate.hidePhone) {
                                        message = 'صاحب الإعلان اختار إخفاء رقم الهاتف';
                                      } else if (_advertiserPhoneNumber == null || _advertiserPhoneNumber!.isEmpty) {
                                        message = 'لم يتم تسجيل رقم هاتف لصاحب الإعلان';
                                      }

                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(content: Text(message)));
                                    }
                                  }))),
                            // إضافة مسافة فقط إذا كان كلا الزرين ظاهرين
                            if (_canShowPhone || _isLoadingAdvertiserData)
                              const SizedBox(width: 12),
                            // زر الواتساب - يظهر فقط إذا كان رقم الهاتف متاح
                            if (_canShowPhone || _isLoadingAdvertiserData)
                              Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.green,
                                      Colors.green.shade700,
                                    ])),
                                child: ElevatedButton.icon(
                                  icon: _isLoadingAdvertiserData
                                      ? const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                                      : const Icon(Icons.message),
                                  label: Text(_isLoadingAdvertiserData ? "تحميل..." : "واتساب"),
                                  style: ElevatedButton.styleFrom(
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    backgroundColor: Colors.transparent,
                                    shadowColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                                  onPressed: () async {
                                    if (_isLoadingAdvertiserData) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                            content: Text('جاري تحميل البيانات...')));
                                      return;
                                    }

                                    if (_canShowPhone && _advertiserPhoneNumber != null) {
                                      // تنظيف رقم الهاتف من الرموز والمسافات
                                      String cleanPhoneNumber = _advertiserPhoneNumber!
                                          .replaceAll(RegExp(r'[^\d+]'), '');

                                      // إضافة رمز الكويت إذا لم يكن موجوداً
                                      if (!cleanPhoneNumber.startsWith('+965') &&
                                          !cleanPhoneNumber.startsWith('965')) {
                                        cleanPhoneNumber = '965$cleanPhoneNumber';
                                      }

                                      final whatsappUrl = "https://wa.me/$cleanPhoneNumber";
                                      final Uri whatsappUri = Uri.parse(whatsappUrl);

                                      if (await canLaunchUrl(whatsappUri)) {
                                        await launchUrl(whatsappUri);
                                      }
                                    } else {
                                      String message = 'رقم الهاتف غير متاح';
                                      if (widget.estate.hidePhone) {
                                        message = 'صاحب الإعلان اختار إخفاء رقم الهاتف';
                                      } else if (_advertiserPhoneNumber == null || _advertiserPhoneNumber!.isEmpty) {
                                        message = 'لم يتم تسجيل رقم هاتف لصاحب الإعلان';
                                      }

                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(content: Text(message)));
                                    }
                                  }))),
                            // رسالة توضيحية عندما تكون الأزرار مخفية
                            if (!_canShowPhone && !_isLoadingAdvertiserData)
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                      width: 1)),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.phone_disabled,
                                        color: Colors.grey.shade600,
                                        size: 32),
                                      const SizedBox(height: 8),
                                      Text(
                                        widget.estate.hidePhone
                                            ? 'صاحب الإعلان اختار إخفاء رقم الهاتف'
                                            : 'لم يتم تسجيل رقم هاتف لصاحب الإعلان',
                                        textAlign: TextAlign.center,
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                          fontWeight: FontWeight.w500)),
                                    ]))),
                          ])),
                    ]))),
            ]),
        ]));
  }



  /// بناء المعلومات الأساسية للعقار بتصميم عصري وشامل
  Widget _buildMainPropertyInfo(Estate estate) {
    final List<Map<String, dynamic>> mainInfo = [];

    // 1. المعلومات الأساسية الرئيسية
    if (estate.mainCategory != null && estate.mainCategory!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.category_outlined,
        'label': "التصنيف الرئيسي",
        'value': estate.mainCategory!,
        'color': AppColors.primary,
        'gradient': [AppColors.primary, AppColors.primaryLight],
        'description': "نوع الإعلان",
      });
    }

    if (estate.subCategory != null && estate.subCategory!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.subdirectory_arrow_right_outlined,
        'label': "التصنيف الفرعي",
        'value': estate.subCategory!,
        'color': AppColors.primaryLight,
        'gradient': [AppColors.primaryLight, AppColors.primary],
        'description': "التصنيف التفصيلي",
      });
    }

    if (estate.propertyType != null && estate.propertyType!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.home_work_outlined,
        'label': "نوع العقار",
        'value': estate.propertyType!,
        'color': AppColors.info,
        'gradient': [AppColors.info, AppColors.primary],
        'description': "تصنيف العقار",
      });
    }

    if (estate.usageType != null && estate.usageType!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.business_outlined,
        'label': "نوع الاستخدام",
        'value': estate.usageType!,
        'color': AppColors.secondary,
        'gradient': [AppColors.secondary, AppColors.secondaryLight],
        'description': "الغرض من العقار",
      });
    }

    // 2. المعلومات المكانية والقياسات
    if (estate.area != null) {
      mainInfo.add({
        'icon': Icons.square_foot_outlined,
        'label': "المساحة",
        'value': "${estate.area} م²",
        'color': AppColors.primary,
        'gradient': [AppColors.primary, AppColors.primaryLight],
        'description': "المساحة الإجمالية للعقار",
      });
    }

    if (estate.numberOfRooms != null) {
      mainInfo.add({
        'icon': Icons.bed_outlined,
        'label': "عدد الغرف",
        'value': estate.numberOfRooms.toString(),
        'color': AppColors.primaryLight,
        'gradient': [AppColors.primaryLight, AppColors.primary],
        'description': "غرف النوم الرئيسية",
      });
    }

    if (estate.numberOfBathrooms != null) {
      mainInfo.add({
        'icon': Icons.bathtub_outlined,
        'label': "عدد الحمامات",
        'value': estate.numberOfBathrooms.toString(),
        'color': AppColors.secondary,
        'gradient': [AppColors.secondary, AppColors.secondaryLight],
        'description': "الحمامات والمرافق الصحية",
      });
    }

    if (estate.floorNumber != null) {
      mainInfo.add({
        'icon': Icons.layers_outlined,
        'label': "رقم الطابق",
        'value': "الطابق ${estate.floorNumber}",
        'color': AppColors.primaryDark,
        'gradient': [AppColors.primaryDark, AppColors.primary],
        'description': "موقع العقار في المبنى",
      });
    }

    if (estate.numberOfFloors != null) {
      mainInfo.add({
        'icon': Icons.stairs_outlined,
        'label': "عدد الأدوار",
        'value': "${estate.numberOfFloors} أدوار",
        'color': AppColors.info,
        'gradient': [AppColors.info, AppColors.primary],
        'description': "إجمالي أدوار المبنى",
      });
    }

    if (estate.buildingAge != null) {
      mainInfo.add({
        'icon': Icons.calendar_today_outlined,
        'label': "عمر البناء",
        'value': "${estate.buildingAge} سنة",
        'color': AppColors.primaryDark,
        'gradient': [AppColors.primaryDark, AppColors.primary],
        'description': "عمر المبنى منذ الإنشاء",
      });
    }

    // 3. المعلومات الداخلية الإضافية
    if (estate.salon != null && estate.salon!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.weekend_outlined,
        'label': "الصالة",
        'value': estate.salon!,
        'color': AppColors.secondary,
        'gradient': [AppColors.secondary, AppColors.secondaryLight],
        'description': "تفاصيل الصالة",
      });
    }

    if (estate.internalLocation != null && estate.internalLocation!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.room_outlined,
        'label': "الموقع الداخلي",
        'value': estate.internalLocation!,
        'color': AppColors.primaryLight,
        'gradient': [AppColors.primaryLight, AppColors.primary],
        'description': "الموقع داخل المجمع",
      });
    }

    if (estate.rebound != null && estate.rebound!.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.border_outer_outlined,
        'label': "الارتداد",
        'value': estate.rebound!,
        'color': AppColors.info,
        'gradient': [AppColors.info, AppColors.primary],
        'description': "مساحة الارتداد",
      });
    }

    // 4. معلومات الموقع
    if (estate.location.isNotEmpty) {
      mainInfo.add({
        'icon': Icons.location_on_outlined,
        'label': "الموقع",
        'value': estate.location,
        'color': AppColors.primaryLight,
        'gradient': [AppColors.primaryLight, AppColors.primary],
        'description': "العنوان والمنطقة",
      });
    }

    // إذا لم تكن هناك معلومات، عرض رسالة
    if (mainInfo.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 12),
            Text(
              "لا توجد معلومات أساسية متاحة",
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // شبكة المعلومات الأساسية
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: mainInfo.length,
          itemBuilder: (context, index) {
            final info = mainInfo[index];
            return _buildModernPropertyCard(
              icon: info['icon'] as IconData,
              label: info['label'] as String,
              value: info['value'] as String,
              color: info['color'] as Color,
              gradient: info['gradient'] as List<Color>,
              description: info['description'] as String,
            );
          },
        ),

        const SizedBox(height: 20),

        // قسم المميزات الإضافية
        _buildAdditionalFeatures(estate),
      ],
    );
  }

  /// بناء بطاقة معلومات العقار العصرية
  Widget _buildModernPropertyCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required List<Color> gradient,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            gradient[0].withValues(alpha: 0.1),
            gradient[1].withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الأيقونة والتسمية
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradient,
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // القيمة
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),

          const SizedBox(height: 4),

          // الوصف
          Text(
            description,
            style: GoogleFonts.cairo(
              fontSize: 10,
              color: AppColors.textLight,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }


}
