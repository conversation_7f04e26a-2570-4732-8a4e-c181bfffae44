// lib/presentation/pages/home_page.dart
import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/constants/user_types.dart';
import 'package:kuwait_corners/domain/services/user_interface_customization_service.dart';
import 'package:kuwait_corners/presentation/pages/all_estates_page.dart';
import 'package:kuwait_corners/presentation/pages/improved_ad_creation_entry.dart';
import 'package:kuwait_corners/presentation/pages/property_request/create_property_request_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/property_requests_page.dart';
import 'package:kuwait_corners/presentation/providers/property_request_provider.dart';
import 'package:provider/provider.dart';
import 'package:kuwait_corners/presentation/widgets/custom_app_bar.dart';
import 'package:kuwait_corners/presentation/widgets/premium_estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/common/custom_drawer.dart';
import 'package:kuwait_corners/presentation/widgets/empty_state_widget.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:shimmer/shimmer.dart';

import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../domain/entities/estate.dart';
// import '../../domain/services/property_comparison_service.dart'; // معلق مؤقتاً
import '../../core/utils/responsive_utils.dart';
import '../widgets/responsive_layout.dart';
import '../../core/widgets/lazy_loading_widget.dart';
import '../bloc/estate_bloc.dart';
import '../../core/services/enhanced_cache_service.dart';
import '../../core/services/enhanced_state_management_service.dart';
import '../bloc/estate_event.dart';
import '../bloc/estate_state.dart';
import '../widgets/estate_card.dart';
import 'advanced_search_page.dart';
import 'favorites_page.dart';
import 'filtered_results_page.dart';
import '../../domain/models/smart_filter_model.dart';
import 'improved_category_selection_page.dart';
import 'copied_estate_details_page.dart';
import '../../domain/entities/estate_factory.dart';
import '../../domain/entities/estate_converter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import 'ads_diagnosis_page.dart';
// import 'property_comparison_page.dart'; // معلق مؤقتاً

/// الصفحة الرئيسية تعرض أقسام الإعلانات العقارية المختلفة مع إمكانية البحث والبنرات الإعلانية.
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  // الخدمات المستخدمة
  // final PropertyComparisonService _comparisonService = PropertyComparisonService(); // معلق مؤقتاً

  // إزالة التخصيص من الصفحة الرئيسية - ستكون موحدة لجميع المستخدمين
  // التخصيص سيكون فقط في الـ Drawer

  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;

  // هنا يمكن جلب روابط البنرات لاحقاً من Firebase أو لوحة التحكم
  // استخدام البانرات الإعلانية المتوفرة في مجلد الأصول
  final List<String> bannerImages = [
    'assets/banners/banner_app.jpg',
    'assets/banners/banner_app1.jpg',
    'assets/banners/banner_app2.jpg',
  ];

  // قائمة الأقسام العقارية فقط مع أيقونات محسنة
  final List<Map<String, dynamic>> sections = [
    {
      "name": "الكل",
      "icon": Icons.home_work,
      "route": null,
      "color": Colors.blue
    },
    {"name": "للبيع", "icon": Icons.sell, "route": null, "color": Colors.green},
    {
      "name": "للإيجار",
      "icon": Icons.apartment,
      "route": null,
      "color": Colors.orange
    },
    {
      "name": "للبدل",
      "icon": Icons.swap_horiz,
      "route": null,
      "color": Colors.purple
    },
    {"name": "منازل", "icon": Icons.villa, "route": null, "color": Colors.teal},
    {
      "name": "شقق",
      "icon": Icons.apartment_outlined,
      "route": null,
      "color": Colors.amber
    },
    {
      "name": "أراضي",
      "icon": Icons.landscape,
      "route": null,
      "color": Colors.brown
    },
    {
      "name": "تجاري",
      "icon": Icons.business,
      "route": null,
      "color": Colors.indigo
    },
    {
      "name": "مكاتب",
      "icon": Icons.business_center,
      "route": null,
      "color": Colors.deepOrange
    },
    {"name": "دولي", "icon": Icons.public, "route": null, "color": Colors.cyan},
  ];

  // القسم المحدد حالياً
  String selectedCategory = "الكل";

  // متحكم حقل البحث والنص المدخل
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = "";
  final FocusNode _searchFocusNode = FocusNode();
  final GlobalKey _searchFieldKey = GlobalKey();

  // متحكم PageView للبانر
  final PageController _bannerController =
      PageController(viewportFraction: 0.98);
  int _currentBannerPage = 0;

  // متغيرات للتحكم بالتحميل والتحديث
  bool _isLoading = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize = 10;

  // متغيرات للفلترة والترتيب
  String _sortBy = "createdAt";
  bool _sortAscending = false;
  final Map<String, dynamic> _filters = {};

  // مفتاح للـ RefreshIndicator
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  // مفتاح للـ ScrollController
  final ScrollController _scrollController = ScrollController();

  // متغير لتتبع ما إذا كانت هذه هي المرة الأولى التي يتم فيها تحميل البيانات
  bool _isFirstLoad = true;

  // متغير لتتبع نوع المستخدم الحالي
  bool _isUserSeeker = false;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800));

    // تم إزالة الرسوم المتحركة غير المستخدمة

    // بدء الرسوم المتحركة
    _animationController.forward();

    // إضافة مستمع للـ ScrollController لتحميل المزيد من البيانات عند الوصول لنهاية القائمة
    _scrollController.addListener(_scrollListener);

    // إزالة تخصيص الواجهة - الصفحة الرئيسية موحدة لجميع المستخدمين

    // التحقق من نوع المستخدم
    _checkUserType();

    // استدعاء حدث جلب العقارات بالتحميل المتدرج عند بدء الصفحة
    _loadEstates(refresh: true);

    // بدء تغيير البانر تلقائياً كل 5 ثوانٍ
    _startBannerAutoScroll();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // إذا لم تكن هذه هي المرة الأولى، فقم بإعادة تحميل البيانات عند العودة إلى الصفحة
    if (!_isFirstLoad) {
      // تأخير قصير لضمان تحميل الصفحة بشكل كامل قبل تحميل البيانات
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadEstates(refresh: true);

          // إعادة تهيئة البانر وبدء التمرير التلقائي
          setState(() {
            _currentBannerPage = 0;
          });

          // إعادة تشغيل التمرير التلقائي للبانر
          _bannerController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut);
          _startBannerAutoScroll();
        }
      });
    }
    _isFirstLoad = false;
  }

  /// التحقق من نوع المستخدم الحالي
  Future<void> _checkUserType() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return;
    }

    try {
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      final userData = userDoc.data();

      if (userData != null) {
        setState(() {
          // التحقق ما إذا كان المستخدم باحث عن عقار (type = 1 or UserType.seeker)
          _isUserSeeker = userData['type'] == 1 ||
                          userData['userType'] == 'seeker' ||
                          userData['userType'] == 'user' ||
                          userData['userType'] == 'property_seeker';
        });
      }
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // تحميل العقارات مع تحسينات الأداء والتخزين المؤقت
  void _loadEstates({bool refresh = false}) {
    // إذا كان التحميل قيد التنفيذ بالفعل ولم يكن هذا تحديثًا إجباريًا، فلا تفعل شيئًا
    if (_isLoading && !refresh) return;

    if (refresh) {
      setState(() {
        _currentPage = 1;
        _hasMoreData = true;
      });
    }

    if (!_hasMoreData && !refresh) return;

    setState(() {
      _isLoading = true;
    });

    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    _loadFromCacheFirst(refresh);
  }

  /// تحميل البيانات من التخزين المؤقت أولاً ثم من الخادم
  Future<void> _loadFromCacheFirst(bool refresh) async {
    try {
      final enhancedCacheService = EnhancedCacheService();
      final stateService = EnhancedStateManagementService();
      final cacheKey = 'page_${_currentPage}_${selectedCategory}_$_searchQuery';

      // بدء عملية التحميل
      stateService.startLoading('estates_loading', message: 'جاري تحميل العقارات...');

      // إذا لم يكن تحديث إجباري، جرب التخزين المؤقت أولاً
      if (!refresh) {
        final cachedResult = await enhancedCacheService.getCachedEstates(cacheKey);
        if (cachedResult != null) {
          // استخدام البيانات المخزنة مؤقتاً
          await _processCachedEstatesData(cachedResult);
          stateService.stopLoading('estates_loading');
          return;
        }
      }

      // تحميل من الخادم
      _loadFromServer(refresh, cacheKey);
    } catch (e) {
      final stateService = EnhancedStateManagementService();
      stateService.addError(ErrorState(
        id: 'cache_error',
        message: 'خطأ في التخزين المؤقت: ${e.toString()}',
        type: ErrorType.unknown,
        isRetryable: true));

      // في حالة الخطأ، تحميل من الخادم مباشرة
      _loadFromServer(refresh, null);
    }
  }

  /// تحميل البيانات من الخادم
  void _loadFromServer(bool refresh, String? cacheKey) {
    // إضافة تأخير قصير لتجنب مشاكل التزامن المحتملة
    Future.microtask(() {
      if (mounted) {
        context.read<EstateBloc>().add(
              FetchPaginatedEstates(
                refresh: refresh,
                page: _currentPage,
                pageSize: _pageSize,
                sortBy: _sortBy,
                sortAscending: _sortAscending,
                filters: _getOptimizedFilters(),
                searchQuery: _searchQuery.trim().isNotEmpty ? _searchQuery : null));
      }
    });
  }

  /// حفظ البيانات في التخزين المؤقت بعد التحميل الناجح
  Future<void> _cacheLoadedData(List<Estate> estates, bool hasMore) async {
    try {
      final enhancedCacheService = EnhancedCacheService();
      final cacheKey = 'page_${_currentPage}_${selectedCategory}_$_searchQuery';

      await enhancedCacheService.cacheEstates(
        key: cacheKey,
        estates: estates,
        hasMore: hasMore,
        currentPage: _currentPage,
        category: selectedCategory != "الكل" ? selectedCategory : null,
        searchQuery: _searchQuery.trim().isNotEmpty ? _searchQuery : null);
    } catch (e) {
      // في حالة فشل التخزين المؤقت، لا نعرض خطأ للمستخدم
      // فقط نسجل الخطأ للمطورين
      print('خطأ في حفظ البيانات في التخزين المؤقت: $e');
    }
  }

  /// معالجة البيانات المخزنة مؤقتاً (الطريقة القديمة)
  void _processCachedData(dynamic cachedData) {
    try {
      if (cachedData is Map<String, dynamic>) {
        final estates = (cachedData['estates'] as List?)
            ?.map((e) => _estateFromMap(e as Map<String, dynamic>))
            .where((estate) => estate != null)
            .cast<Estate>()
            .toList() ?? [];

        setState(() {
          _isLoading = false;
          _hasMoreData = cachedData['hasMore'] ?? false;
        });

        // محاكاة إرسال البيانات المخزنة إلى BLoC
        // يمكن تحسين هذا لاحقاً بإضافة event جديد للبيانات المخزنة
        if (estates.isNotEmpty) {
          // استخدام البيانات المخزنة مؤقتاً
          // يمكن إضافة معالجة خاصة هنا
        }
      }
    } catch (e) {
      // في حالة الخطأ، تحميل من الخادم
      _loadFromServer(false, null);
    }
  }

  /// معالجة البيانات المخزنة مؤقتاً (الطريقة المحسنة)
  Future<void> _processCachedEstatesData(CachedEstatesResult cachedResult) async {
    try {
      setState(() {
        _isLoading = false;
        _hasMoreData = cachedResult.hasMore;
        if (cachedResult.currentPage != null) {
          _currentPage = cachedResult.currentPage!;
        }
      });

      // إرسال البيانات المخزنة إلى BLoC
      if (cachedResult.estates.isNotEmpty) {
        // محاكاة حالة تحميل ناجح
        context.read<EstateBloc>().add(LoadCachedEstatesEvent(cachedResult.estates));
      }
    } catch (e) {
      final stateService = EnhancedStateManagementService();
      stateService.addError(ErrorState(
        id: 'cached_data_processing_error',
        message: 'خطأ في معالجة البيانات المخزنة: ${e.toString()}',
        type: ErrorType.unknown,
        isRetryable: true));

      // في حالة الخطأ، تحميل من الخادم
      _loadFromServer(false, null);
    }
  }

  /// الحصول على فلاتر محسنة للاستعلام
  Map<String, dynamic> _getOptimizedFilters() {
    final optimizedFilters = Map<String, dynamic>.from(_filters);

    // إضافة فلتر القسم المحدد
    if (selectedCategory != "الكل" && selectedCategory != "الرئيسية") {
      // تحسين مطابقة التصنيفات
      optimizedFilters['category'] = _getCategoryFilter(selectedCategory);
    }

    // إضافة فلاتر إضافية حسب الحاجة
    // في بيئة التطوير: عرض جميع الإعلانات للاختبار
    // في بيئة الإنتاج: عرض فقط الإعلانات المدفوعة
    // optimizedFilters['isPaymentVerified'] = true; // تم تعطيله مؤقتاً للاختبار

    return optimizedFilters;
  }

  /// الحصول على فلتر التصنيف المحسن
  Map<String, dynamic> _getCategoryFilter(String category) {
    // خريطة تحويل أسماء الأقسام إلى قيم قاعدة البيانات الفعلية
    final categoryMap = {
      // فلاتر التصنيف الرئيسي
      'للبيع': {'mainCategory': 'عقار للبيع'},
      'للإيجار': {'mainCategory': 'عقار للايجار'},
      'للبدل': {'mainCategory': 'عقار للبدل'},
      'دولي': {'mainCategory': 'عقار دولي'},
      'تجاري': {'mainCategory': 'تجاري'},

      // فلاتر التصنيف الفرعي - البحث في subCategory
      'منازل': {'subCategoryContains': 'بيت'},
      'شقق': {'subCategoryContains': 'شقة'},
      'أراضي': {'subCategoryContains': 'اراضي'},
      'مكاتب': {'subCategoryContains': 'مكتب'},
      'محلات': {'subCategoryContains': 'محل'},
      'مزارع': {'subCategoryContains': 'مزارع'},
      'عمارات': {'subCategoryContains': 'عمارة'},
    };

    return categoryMap[category] ?? {'searchText': category};
  }

  /// فلترة العقارات حسب التصنيف بطريقة محسنة
  List<Estate> _filterEstatesByCategory(List<Estate> estates, String category) {
    if (category == "الكل" || category == "الرئيسية") {
      return estates;
    }

    // طباعة تشخيصية لفهم البيانات الموجودة
    if (estates.isNotEmpty) {
      print('🔍 تشخيص الفلترة للقسم: $category');
      print('📊 عدد العقارات الكلي: ${estates.length}');

      // طباعة عينة من البيانات
      for (int i = 0; i < estates.length && i < 3; i++) {
        final estate = estates[i];
        print('📋 عقار ${i + 1}:');
        print('  العنوان: ${estate.title}');
        print('  التصنيف الرئيسي: "${estate.mainCategory}"');
        print('  التصنيف الفرعي: "${estate.subCategory}"');
      }
    }

    final filteredEstates = estates.where((estate) {
      // الحصول على فلتر التصنيف
      final categoryFilter = _getCategoryFilter(category);

      // فحص التصنيف الرئيسي المطابق تماماً
      if (categoryFilter['mainCategory'] != null) {
        final matches = estate.mainCategory == categoryFilter['mainCategory'];
        if (matches) {
          print('✅ تطابق في التصنيف الرئيسي: ${estate.title}');
        }
        return matches;
      }

      // فحص البحث في التصنيف الفرعي
      if (categoryFilter['subCategoryContains'] != null) {
        final searchTerm = categoryFilter['subCategoryContains'] as String;
        final matches = estate.subCategory?.contains(searchTerm) == true;
        if (matches) {
          print('✅ تطابق في التصنيف الفرعي: ${estate.title}');
        }
        return matches;
      }

      // فحص البحث النصي العام
      if (categoryFilter['searchText'] != null) {
        final searchTerm = (categoryFilter['searchText'] as String).toLowerCase();
        final titleMatch = estate.title.toLowerCase().contains(searchTerm);
        final mainCategoryMatch = estate.mainCategory?.toLowerCase().contains(searchTerm) == true;
        final subCategoryMatch = estate.subCategory?.toLowerCase().contains(searchTerm) == true;

        final matches = titleMatch || mainCategoryMatch || subCategoryMatch;
        if (matches) {
          print('✅ تطابق في البحث النصي: ${estate.title}');
        }
        return matches;
      }

      return false;
    }).toList();

    print('📈 نتائج الفلترة: ${filteredEstates.length} عقار');
    return filteredEstates;
  }

  /// تحويل Map إلى Estate
  Estate? _estateFromMap(Map<String, dynamic> map) {
    try {
      return Estate(
        id: map['id'] ?? '',
        title: map['title'] ?? '',
        description: map['description'] ?? '',
        price: (map['price'] ?? 0.0).toDouble(),
        location: map['location'] ?? '',
        photoUrls: List<String>.from(map['photoUrls'] ?? []),
        isFeatured: map['isFeatured'] ?? false,
        planType: map['planType'] ?? 'free',
        startDate: map['startDate'] != null
            ? DateTime.parse(map['startDate'])
            : null,
        endDate: map['endDate'] != null
            ? DateTime.parse(map['endDate'])
            : null,
        createdAt: map['createdAt'] != null
            ? DateTime.parse(map['createdAt'])
            : DateTime.now(),
        mainCategory: map['mainCategory'],
        subCategory: map['subCategory'],
        postedByUserType: map['postedByUserType'],
        hidePhone: map['hidePhone'] ?? false,
        extraPhones: List<String>.from(map['extraPhones'] ?? []),
        shareLocation: map['shareLocation'] ?? false,
        lat: map['lat']?.toDouble(),
        lng: map['lng']?.toDouble(),
        hasCentralAC: map['hasCentralAC'] ?? false,
        hasSecurity: map['hasSecurity'],
        allowPets: map['allowPets'],
        hasElevator: map['hasElevator'],
        hasSwimmingPool: map['hasSwimmingPool'],
        hasMaidRoom: map['hasMaidRoom'] ?? false,
        hasGarage: map['hasGarage'] ?? false,
        hasBalcony: map['hasBalcony'],
        isFullyFurnished: map['isFullyFurnished'],
        rebound: map['rebound'],
        numberOfRooms: map['numberOfRooms'],
        internalLocation: map['internalLocation'],
        salon: map['salon'],
        area: map['area']?.toDouble(),
        floorNumber: map['floorNumber'],
        numberOfBathrooms: map['numberOfBathrooms'],
        buildingAge: map['buildingAge'],
        numberOfFloors: map['numberOfFloors'],
        propertyType: map['propertyType'],
        autoRepublish: map['autoRepublish'] ?? false,
        kuwaitCornersPin: map['kuwaitCornersPin'] ?? false,
        movingAd: map['movingAd'] ?? false,
        vipBadge: map['vipBadge'] ?? false,
        pinnedOnHome: map['pinnedOnHome'] ?? false,
        discountCode: map['discountCode'],
        advertiserImage: map['advertiserImage'],
        advertiserName: map['advertiserName'],
        advertiserEmail: map['advertiserEmail'],
        advertiserRegistrationDate: map['advertiserRegistrationDate'] != null
            ? DateTime.parse(map['advertiserRegistrationDate'])
            : null,
        advertiserAdsCount: map['advertiserAdsCount'],
        ownerId: map['ownerId'],
        originalEstateId: map['originalEstateId'],
        isOriginal: map['isOriginal'] ?? true,
        copiedBy: List<String>.from(map['copiedBy'] ?? []),
        isPaymentVerified: map['isPaymentVerified'] ?? false,
        viewsCount: map['viewsCount'],
        inquiriesCount: map['inquiriesCount'],
        favoritesCount: map['favoritesCount'],
        latitude: map['latitude']?.toDouble(),
        longitude: map['longitude']?.toDouble(),
        rooms: map['rooms'],
        bathrooms: map['bathrooms'],
        floors: map['floors'],
        purpose: map['purpose'],
        usageType: map['usageType'],
        hasGarden: map['hasGarden'],
        hasPool: map['hasPool'],
        hasDriverRoom: map['hasDriverRoom'],
        hasPrivateEntrance: map['hasPrivateEntrance'],
        hasEquippedKitchen: map['hasEquippedKitchen'],
        isAvailable: map['isAvailable'] ?? true,
        isCopied: map['isCopied'] ?? false,
        copiedAt: map['copiedAt'] != null
            ? DateTime.parse(map['copiedAt'])
            : null,
        isPaidAd: map['isPaidAd'] ?? false);
    } catch (e) {
      print('خطأ في تحويل Map إلى Estate: $e');
      return null;
    }
  }

  // مستمع التمرير لتحميل المزيد من البيانات
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMoreData) {
        setState(() {
          _currentPage++;
        });
        _loadEstates();
      }
    }
  }

  // متغير للتحكم في مؤقت البانر
  Timer? _bannerTimer;

  // بدء تغيير البانر تلقائياً
  void _startBannerAutoScroll() {
    // إلغاء المؤقت السابق إذا كان موجوداً
    _bannerTimer?.cancel();

    // إنشاء مؤقت جديد
    _bannerTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        if (_currentBannerPage < bannerImages.length - 1) {
          _currentBannerPage++;
        } else {
          _currentBannerPage = 0;
        }

        _bannerController.animateToPage(
          _currentBannerPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut);
      } else {
        // إلغاء المؤقت إذا لم تعد الصفحة مثبتة
        timer.cancel();
      }
    });
  }

  // تم إزالة تخصيص الواجهة - الصفحة الرئيسية موحدة لجميع المستخدمين

  // تحديث البيانات عند سحب الشاشة للأسفل
  Future<void> _refreshData() async {
    _loadEstates(refresh: true);
    return Future.delayed(const Duration(seconds: 1));
  }

  /// تحميل صفحة من العقارات للتحميل الكسول
  Future<List<Estate>> _loadEstatesPage(int page, int limit) async {
    try {
      // تحميل البيانات من Firebase
      Query query = FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .orderBy('createdAt', descending: true);

      // تطبيق الفلاتر إذا كانت موجودة
      if (selectedCategory != "الكل") {
        final categoryFilter = _getCategoryFilter(selectedCategory);
        if (categoryFilter.isNotEmpty) {
          if (categoryFilter.containsKey('mainCategory')) {
            query = query.where('mainCategory', isEqualTo: categoryFilter['mainCategory']);
          }
        }
      }

      // تطبيق التصفح بالصفحات
      if (page > 0) {
        query = query.startAfter([DateTime.now().subtract(Duration(days: page * 7))]);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      final estates = <Estate>[];

      for (final doc in querySnapshot.docs) {
        try {
          final estateBase = EstateFactory.createFromSnapshot(doc);
          final estate = EstateConverter.toLegacyEstate(estateBase);
          if (estate != null) {
            estates.add(estate);
          }
        } catch (e) {
          debugPrint('خطأ في تحويل العقار: $e');
        }
      }

      // تطبيق البحث النصي إذا كان موجود
      if (_searchQuery.isNotEmpty) {
        return estates.where((estate) {
          final titleMatch = estate.title.toLowerCase().contains(_searchQuery.toLowerCase());
          final locationMatch = estate.location.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false;
          return titleMatch || locationMatch;
        }).toList();
      }

      return estates;
    } catch (e) {
      debugPrint('خطأ في تحميل صفحة العقارات: $e');
      return [];
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    _bannerController.dispose();
    // إلغاء المؤقت عند التخلص من الصفحة
    _bannerTimer?.cancel();
    super.dispose();
  }

  /// widget لبناء بانر إعلاني علوي يستخدم PageView مع شيمر افتراضي للبنرات الفارغة
  Widget _buildBannerSlider() {
    final bannerHeight = ResponsiveUtils.isMobile(context) ? 180.0 : 220.0;
    final borderRadius = ResponsiveUtils.getBorderRadius(context);
    final spacing = ResponsiveUtils.getSpacing(context, SpacingType.small);

    return Column(
      children: [
        SizedBox(
          height: bannerHeight,
          width: double.infinity,
          child: PageView.builder(
            itemCount: bannerImages.length,
            controller: _bannerController,
            onPageChanged: (index) {
              setState(() {
                _currentBannerPage = index;
              });
            },
            itemBuilder: (context, index) {
              final imageUrl = bannerImages[index];
              return Container(
                margin: EdgeInsets.symmetric(horizontal: spacing / 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withAlpha(38),
                      blurRadius: 8,
                      offset: const Offset(0, 3)),
                  ],
                  image: imageUrl.isNotEmpty
                      ? DecorationImage(
                          image: AssetImage(imageUrl),
                          fit: BoxFit.cover)
                      : null),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: imageUrl.isEmpty
                      ? _buildShimmerBanner()
                      : Container(
                          color: Colors.transparent,
                          width: double.infinity,
                          height: double.infinity)));
            })),
        SizedBox(height: spacing),
        // مؤشر الصفحة الحالية بتصميم متجاوب
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(bannerImages.length, (index) {
            final indicatorSize = ResponsiveUtils.isMobile(context) ? 6.0 : 8.0;
            final activeIndicatorSize = ResponsiveUtils.isMobile(context) ? 12.0 : 16.0;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: EdgeInsets.symmetric(horizontal: spacing / 4),
              width: _currentBannerPage == index ? activeIndicatorSize : indicatorSize,
              height: indicatorSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(indicatorSize / 2),
                color: _currentBannerPage == index
                    ? AppColors.primary
                    : Colors.grey.shade300));
          })),
      ]);
  }

  /// widget لعرض تأثير الشيمر على البنر الفارغ
  Widget _buildShimmerBanner() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.0)),
        child: const Center(
          child: Text(
            'جاري تحميل الإعلانات',
            style: TextStyle(color: Colors.transparent)))));
  }

  // تم إزالة شريط الأدوات المخصص - الصفحة الرئيسية موحدة

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      drawer: const CustomDrawer(),
      appBar: CustomAppBar(
        title: "الصفحة الرئيسية",
        showSearchButton: true,
        showNotificationButton: true,
        showPropertyRequestsButton: true,
        additionalActions: [
          // زر تشخيص الإعلانات (للمطورين)
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.bug_report, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdsDiagnosisPage(),
                  ),
                );
              },
              tooltip: 'تشخيص الإعلانات',
            ),
          ),
        ],
        onSearchPressed: () {
          // التركيز على حقل البحث
          FocusScope.of(context).requestFocus(_searchFocusNode);
          _searchController.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _searchController.text.length);
        },
        onPropertyRequestsPressed: () {
          // الانتقال إلى صفحة طلبات العقارات
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PropertyRequestsPage()));
        }),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: () => _refreshData(),
        child: ResponsiveContainer(
          child: ListView(
            controller: _scrollController,
            children: [
              // عرض البانر الإعلاني العلوي مع إمكانية التمرير الأفقي
              _buildBannerSlider(),
              const SizedBox(height: 16),

              // حقل البحث بتصميم عصري ومحسن - بدون ظل
              Container(
                height: 48, // تحديد ارتفاع ثابت للحقل
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  // إزالة الظل كما طلب المستخدم
                ),
                child: TextField(
                  key: _searchFieldKey, // إضافة مفتاح للوصول إلى حقل البحث
                  controller: _searchController,
                  focusNode: _searchFocusNode, // ربط نقطة التركيز
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    hintText: "ابحث عن عقار...",
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 14),
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColors.primary,
                      size: 20),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey.shade400,
                              size: 18),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = "";
                              });
                              // تركيز حقل البحث بعد المسح
                              FocusScope.of(context)
                                  .requestFocus(_searchFocusNode);
                            })
                        : null,
                    filled: true,
                    fillColor:
                        Colors.grey.shade50, // تغيير لون الخلفية ليكون أفتح
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide:
                          BorderSide(color: Colors.grey.shade200, width: 1)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide:
                          BorderSide(color: Colors.grey.shade200, width: 1)),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide:
                          BorderSide(color: AppColors.primary, width: 1.5)),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 0)))),

              const SizedBox(height: 16),

              // شريط أدوات البحث والفلترة - تصميم بسيط وأنيق
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: FutureBuilder<bool>(
                  future: _shouldShowFavoritesButtonAsync(),
                  builder: (context, snapshot) {
                    final shouldShowFavorites = snapshot.data ?? false;

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // زر البحث المتقدم
                        Expanded(
                          child: InkWell(
                            onTap: () => _navigateToAdvancedSearch(),
                            borderRadius: BorderRadius.circular(8),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.search_rounded,
                                  color: AppColors.primary,
                                  size: 22),
                                const SizedBox(height: 4),
                                Text(
                                  "بحث متقدم",
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500)),
                                const SizedBox(height: 4),
                                Container(
                                  height: 2,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(2))),
                              ]))),

                        // زر المفضلة (فقط للمستخدمين غير الشركات العقارية)
                        if (shouldShowFavorites)
                          Expanded(
                            child: InkWell(
                              onTap: () => _navigateToFavorites(),
                              borderRadius: BorderRadius.circular(8),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.favorite_rounded,
                                    color: Colors.red.shade600,
                                    size: 22),
                                  const SizedBox(height: 4),
                                  Text(
                                    "المفضلة",
                                    style: TextStyle(
                                      color: Colors.red.shade600,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500)),
                                  const SizedBox(height: 4),
                                  Container(
                                    height: 2,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade600,
                                      borderRadius: BorderRadius.circular(2))),
                                ]))),

                        // زر الفلترة
                        Expanded(
                          child: InkWell(
                            onTap: () => _showFilterDialog(),
                            borderRadius: BorderRadius.circular(8),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.filter_alt_rounded,
                                  color: Colors.amber.shade700,
                                  size: 22),
                                const SizedBox(height: 4),
                                Text(
                                  "فلتر",
                                  style: TextStyle(
                                    color: Colors.amber.shade700,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500)),
                                const SizedBox(height: 4),
                                Container(
                                  height: 2,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.amber.shade700,
                                    borderRadius: BorderRadius.circular(2))),
                              ]))),

                        // زر اللوبي
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              Navigator.pushNamed(context, '/modern-forum');
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.forum_rounded,
                                  color: Colors.teal.shade600,
                                  size: 22),
                                const SizedBox(height: 4),
                                Text(
                                  "Lobby",
                                  style: TextStyle(
                                    color: Colors.teal.shade600,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500)),
                                const SizedBox(height: 4),
                                Container(
                                  height: 2,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.teal.shade600,
                                    borderRadius: BorderRadius.circular(2))),
                              ]))),
                      ]);
                  })),

              const SizedBox(height: 20),

              // شريط تصنيفات العقارات المخصص حسب نوع المستخدم
              _buildCustomizedCategoriesSection(),

              const SizedBox(height: 20),

              // بناء المحتوى حسب حالة البحث والقسم المختار
              _buildSectionContent(),

              // مؤشر التحميل في نهاية القائمة
              if (_isLoading && _currentPage > 1)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Center(
                    child: CircularProgressIndicator())),
            ]))),
      floatingActionButton: _isUserSeeker
          ? _buildPropertyRequestFAB()
          : FutureBuilder<bool>(
              future: _shouldShowAddButtonAsync(),
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data == true) {
                  return _buildResponsiveFAB();
                }
                return const SizedBox.shrink();
              }));
  }

  /// تحديد ما إذا كان يجب عرض زر الإضافة (غير متزامن)
  Future<bool> _shouldShowAddButtonAsync() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;

    try {
      // استخدام UserInterfaceCustomizationService للتحقق من نوع المستخدم
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();

      // زر الإضافة يظهر للجميع عدا الباحثين
      return UserTypeConstants.canPostAds(userType.toString().split('.').last);
    } catch (e) {
      print('Error checking user type for add button: $e');
      return false;
    }
  }

  /// تحديد ما إذا كان يجب عرض زر المفضلة (غير متزامن)
  Future<bool> _shouldShowFavoritesButtonAsync() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;

    try {
      // استخدام UserInterfaceCustomizationService للتحقق من نوع المستخدم
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;

      // زر المفضلة يظهر للجميع عدا الشركات العقارية
      return UserTypeConstants.canAccessFavorites(userTypeString);
    } catch (e) {
      print('Error checking user type for favorites button: $e');
      // في حالة الخطأ، نخفي الزر للأمان
      return false;
    }
  }

  /// بناء زر إنشاء طلب عقار للباحثين عن عقار
  Widget _buildPropertyRequestFAB() {
    final fabSize = ResponsiveUtils.isMobile(context) ? 60.0 : 70.0;
    final borderRadius = fabSize / 2;

    return Container(
      width: fabSize,
      height: fabSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          colors: [
            Colors.orange.shade600,
            Colors.orange.shade700,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.shade600.withAlpha(76),
            blurRadius: 8,
            offset: const Offset(0, 3),
            spreadRadius: 1),
        ]),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () async {
            // الانتقال إلى صفحة إنشاء طلب عقار
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CreatePropertyRequestPage()));

            // إذا تم إنشاء الطلب بنجاح، تحديث البيانات والانتقال إلى صفحة الطلبات
            if (result == true && mounted) {
              // تحديث بيانات الطلبات العقارية
              try {
                final propertyRequestProvider = Provider.of<PropertyRequestProvider>(context, listen: false);
                await propertyRequestProvider.loadPropertyRequests(refresh: true);
              } catch (e) {
                debugPrint('Error refreshing property requests: $e');
              }

              // الانتقال إلى صفحة الطلبات
              if (mounted) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PropertyRequestsPage()));
              }
            }
          },
          child: Container(
            width: fabSize,
            height: fabSize,
            alignment: Alignment.center,
            child: ResponsiveIcon(
              Icons.add_home_work_rounded,
              sizeType: IconSizeType.large,
              color: Colors.white)))));
  }

  /// بناء زر الإضافة العائم المتجاوب
  Widget _buildResponsiveFAB() {
    final fabSize = ResponsiveUtils.isMobile(context) ? 60.0 : 70.0;
    final borderRadius = fabSize / 2;

    return Container(
      width: fabSize,
      height: fabSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(76),
            blurRadius: 8,
            offset: const Offset(0, 3),
            spreadRadius: 1),
        ]),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () {
            // الانتقال إلى صفحة إنشاء الإعلان المحسنة
            Navigator.pushNamed(context, '/improved-ad-creation');
          },
          child: Container(
            width: fabSize,
            height: fabSize,
            alignment: Alignment.center,
            child: ResponsiveIcon(
              Icons.add,
              sizeType: IconSizeType.large,
              color: Colors.white)))));
  }

  /// بناء شريط الأقسام الموحد لجميع المستخدمين
  Widget _buildCustomizedCategoriesSection() {
    // استخدام الأقسام الافتراضية الموحدة لجميع المستخدمين
    final sectionsToUse = sections;
    final sectionHeight = ResponsiveUtils.isMobile(context) ? 90.0 : 110.0;
    final sectionWidth = ResponsiveUtils.isMobile(context) ? 70.0 : 85.0;
    final iconSize = ResponsiveUtils.getIconSize(context, IconSizeType.medium);
    final spacing = ResponsiveUtils.getSpacing(context, SpacingType.small);

    return Container(
      height: sectionHeight,
      margin: EdgeInsets.only(bottom: spacing),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: sectionsToUse.length,
        itemBuilder: (context, index) {
          final section = sectionsToUse[index];
          final bool isSelected = section["name"] == selectedCategory;
          final String sectionName = section["name"] as String;
          final Color sectionColor = section["color"] as Color;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedCategory = sectionName;
              });

              // إذا كان القسم له route، انتقل إليه
              final route = section["route"] as String?;
              if (route != null) {
                _handleToolTap(section);
              }
            },
            child: Container(
              width: sectionWidth,
              margin: EdgeInsets.symmetric(horizontal: spacing / 2),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة القسم مع تأثير بصري محسن
                  Container(
                    height: sectionWidth * 0.7,
                    width: sectionWidth * 0.7,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? sectionColor.withAlpha(30)
                          : Colors.grey.shade50,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? sectionColor
                            : Colors.grey.shade200,
                        width: isSelected ? 1.5 : 0.5),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: sectionColor.withAlpha(60),
                                blurRadius: 8,
                                spreadRadius: 1)
                            ]
                          : null),
                    child: Icon(
                      section["icon"] as IconData,
                      color: isSelected
                          ? sectionColor
                          : Colors.grey.shade600,
                      size: iconSize)),

                  SizedBox(height: spacing / 2),

                  // اسم القسم بتصميم واضح
                  ResponsiveText(
                    sectionName,
                    sizeType: FontSizeType.small,
                    color: isSelected
                        ? sectionColor
                        : Colors.grey.shade700,
                    fontWeight: isSelected
                        ? FontWeight.bold
                        : FontWeight.normal,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),

                  // مؤشر التحديد
                  if (isSelected)
                    Container(
                      margin: EdgeInsets.only(top: spacing / 4),
                      height: 3,
                      width: 20,
                      decoration: BoxDecoration(
                        color: sectionColor,
                        borderRadius: BorderRadius.circular(2))),
                ])));
        }));
  }

  /// عرض مربع حوار الفلترة بتصميم عصري وذكي
  void _showFilterDialog() {
    // متغيرات مؤقتة للفلترة
    final Map<String, dynamic> tempFilters =
        Map<String, dynamic>.from(_filters);

    // نطاق السعر (50 - 10,000 د.ك)
    RangeValues priceRange = RangeValues(
      tempFilters['minPrice']?.toDouble() ?? 50.0,
      tempFilters['maxPrice']?.toDouble() ?? 10000.0);

    // نوع العقار المحدد
    String? selectedPropertyType = tempFilters['propertyType'] as String?;

    // نوع الاستغلال المحدد
    String? selectedUsageType = tempFilters['usageType'] as String?;

    // عدد الغرف
    int? selectedRooms = tempFilters['rooms'] as int?;

    // المساحة
    RangeValues areaRange = RangeValues(
      tempFilters['minArea']?.toDouble() ?? 0.0,
      tempFilters['maxArea']?.toDouble() ?? 1000.0);

    // المناطق المحددة
    List<String> selectedLocations =
        List<String>.from(tempFilters['locations'] ?? []);

    // قائمة المناطق المتاحة (يمكن جلبها من قاعدة البيانات)
    final List<String> availableLocations = [
      "الكويت العاصمة",
      "حولي",
      "الفروانية",
      "مبارك الكبير",
      "الأحمدي",
      "الجهراء",
    ];

    // قائمة أنواع العقارات (الأنواع المعتمدة في قاعدة البيانات)
    final List<String> propertyTypes = [
      "شقة",
      "منزل",
      "بيت",
      "أرض",
      "مكتب",
      "محل تجاري",
      "مخزن",
    ];

    // قائمة أنواع الاستغلال مع القيم الصحيحة (جميع الأنواع المتاحة)
    final Map<String, String> usageTypesMap = {
      "للبيع": "sale",
      "للإيجار": "rent",
      "للبدل": "swap",
      "استثمار": "investment",
      "إدارة أملاك": "management",
    };
    final List<String> usageTypes = usageTypesMap.keys.toList();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: double.maxFinite,
            padding: const EdgeInsets.all(20),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // عنوان الفلترة
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withAlpha(20),
                          shape: BoxShape.circle),
                        child: Icon(
                          Icons.filter_alt,
                          color: AppColors.primary,
                          size: 20)),
                      const SizedBox(width: 12),
                      const Text(
                        "فلتر ذكي للعقارات",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold)),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints()),
                    ]),

                  const SizedBox(height: 20),

                  // رسالة توضيحية
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200)),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade600,
                          size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'يتم تطبيق كل الإعدادات المختارة على تصفية الإعلانات حسب ما تم اختياره من المستخدم',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500))),
                      ])),

                  const SizedBox(height: 16),

                  // نوع الاستغلال
                  _buildFilterSection(
                    title: "نوع الاستغلال",
                    icon: Icons.business_center,
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: usageTypes.map((type) {
                        final bool isSelected = selectedUsageType == type;
                        return FilterChip(
                          label: Text(type),
                          selected: isSelected,
                          checkmarkColor: Colors.white,
                          selectedColor: AppColors.primary,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                          onSelected: (selected) {
                            setState(() {
                              selectedUsageType = selected ? type : null;
                            });
                          });
                      }).toList())),

                  const SizedBox(height: 16),

                  // نوع العقار
                  _buildFilterSection(
                    title: "نوع العقار",
                    icon: Icons.home_work,
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: propertyTypes.map((type) {
                        final bool isSelected = selectedPropertyType == type;
                        return FilterChip(
                          label: Text(type),
                          selected: isSelected,
                          checkmarkColor: Colors.white,
                          selectedColor: AppColors.primary,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                          onSelected: (selected) {
                            setState(() {
                              selectedPropertyType = selected ? type : null;
                            });
                          });
                      }).toList())),

                  const SizedBox(height: 16),

                  // نطاق السعر
                  _buildFilterSection(
                    title: "نطاق السعر (د.ك)",
                    icon: Icons.attach_money,
                    child: Column(
                      children: [
                        RangeSlider(
                          values: priceRange,
                          min: 50,
                          max: 10000,
                          divisions: 100,
                          labels: RangeLabels(
                            priceRange.start.round().toString(),
                            priceRange.end.round().toString()),
                          onChanged: (values) {
                            setState(() {
                              priceRange = values;
                            });
                          },
                          activeColor: AppColors.primary),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${priceRange.start.round()} د.ك",
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold)),
                            Text(
                              "${priceRange.end.round()} د.ك",
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold)),
                          ]),
                      ])),

                  const SizedBox(height: 16),

                  // عدد الغرف
                  _buildFilterSection(
                    title: "عدد الغرف",
                    icon: Icons.bed,
                    child: Wrap(
                      spacing: 8,
                      children: List.generate(6, (index) {
                        final int rooms = index + 1;
                        final bool isSelected = selectedRooms == rooms;
                        return ChoiceChip(
                          label: Text(
                            index < 5 ? "$rooms" : "$rooms+",
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.black87)),
                          selected: isSelected,
                          selectedColor: AppColors.primary,
                          onSelected: (selected) {
                            setState(() {
                              selectedRooms = selected ? rooms : null;
                            });
                          });
                      }))),

                  const SizedBox(height: 16),

                  // المساحة
                  _buildFilterSection(
                    title: "المساحة (م²)",
                    icon: Icons.square_foot,
                    child: Column(
                      children: [
                        RangeSlider(
                          values: areaRange,
                          min: 0,
                          max: 1000,
                          divisions: 100,
                          labels: RangeLabels(
                            areaRange.start.round().toString(),
                            areaRange.end.round().toString()),
                          onChanged: (values) {
                            setState(() {
                              areaRange = values;
                            });
                          },
                          activeColor: AppColors.primary),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${areaRange.start.round()} م²",
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold)),
                            Text(
                              "${areaRange.end.round()} م²",
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold)),
                          ]),
                      ])),

                  const SizedBox(height: 16),

                  // المناطق
                  _buildFilterSection(
                    title: "المناطق",
                    icon: Icons.location_on,
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: availableLocations.map((location) {
                        final bool isSelected =
                            selectedLocations.contains(location);
                        return FilterChip(
                          label: Text(location),
                          selected: isSelected,
                          checkmarkColor: Colors.white,
                          selectedColor: AppColors.primary,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                selectedLocations.add(location);
                              } else {
                                selectedLocations.remove(location);
                              }
                            });
                          });
                      }).toList())),

                  const SizedBox(height: 24),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // زر إعادة الضبط
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            priceRange = const RangeValues(50, 10000);
                            selectedUsageType = null;
                            selectedPropertyType = null;
                            selectedRooms = null;
                            areaRange = const RangeValues(0, 1000);
                            selectedLocations = [];
                          });
                        },
                        icon: const Icon(Icons.refresh, size: 18),
                        label: const Text("إعادة ضبط"),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey.shade700)),

                      Row(
                        children: [
                          // زر الإلغاء
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.grey.shade700),
                            child: const Text("إلغاء")),
                          const SizedBox(width: 8),
                          // زر التطبيق
                          ElevatedButton(
                            onPressed: () {
                              // تحديث الفلاتر
                              tempFilters['minPrice'] =
                                  priceRange.start.round();
                              tempFilters['maxPrice'] = priceRange.end.round();
                              tempFilters['usageType'] = selectedUsageType;
                              tempFilters['propertyType'] =
                                  selectedPropertyType;
                              tempFilters['rooms'] = selectedRooms;
                              tempFilters['minArea'] = areaRange.start.round();
                              tempFilters['maxArea'] = areaRange.end.round();
                              tempFilters['locations'] = selectedLocations;

                              Navigator.pop(context);

                              // تحويل نوع الاستغلال من النص العربي إلى القيمة الإنجليزية
                              String? convertedUsageType;
                              if (selectedUsageType != null) {
                                convertedUsageType = usageTypesMap[selectedUsageType];
                              }

                              print('🔍 تطبيق فلاتر: نوع الاستغلال العربي: $selectedUsageType');
                              print('🔍 تطبيق فلاتر: نوع الاستغلال المحول: $convertedUsageType');

                              // إنشاء نموذج الفلتر الذكي
                              final smartFilter = SmartFilterModel(
                                usageType: convertedUsageType,
                                mainCategory: selectedPropertyType,
                                minPrice: priceRange.start.round().toDouble(),
                                maxPrice: priceRange.end.round().toDouble(),
                                numberOfRooms: selectedRooms,
                                minArea: areaRange.start.round().toDouble(),
                                maxArea: areaRange.end.round().toDouble(),
                                governorate: selectedLocations.isNotEmpty ? selectedLocations.first : null,
                                sortBy: 'createdAt',
                                descending: true);

                              // الانتقال إلى صفحة نتائج البحث المفلترة
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => FilteredResultsPage(
                                    filter: smartFilter)));
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8))),
                            child: const Text("تطبيق")),
                        ]),
                    ]),
                ]))))));
  }

  // Funciones eliminadas para evitar errores

  /// تحديد نوع البطاقة بناءً على خصائص العقار وعنوان القسم
  PremiumCardType _getCardTypeForEstate(Estate estate, String sectionTitle) {
    if (sectionTitle == "إعلانات مميزة" || estate.isFeatured) {
      return PremiumCardType.featured;
    } else if (estate.vipBadge == true) {
      return PremiumCardType.vip;
    } else if (estate.kuwaitCornersPin == true) {
      return PremiumCardType.pinned;
    } else if (estate.pinnedOnHome == true) {
      return PremiumCardType.pinnedOnHome;
    } else if (estate.movingAd == true) {
      return PremiumCardType.moving;
    }
    return PremiumCardType.normal;
  }

  /// بناء قسم فلترة
  Widget _buildFilterSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14)),
            ]),
          const SizedBox(height: 12),
          child,
        ]));
  }

  /// عرض مربع حوار الترتيب بتصميم عصري وذكي
  void _showSortDialog() {
    // نسخة مؤقتة من متغيرات الترتيب
    String tempSortBy = _sortBy;
    bool tempSortAscending = _sortAscending;

    // قائمة خيارات الترتيب المتاحة
    final List<Map<String, dynamic>> sortOptions = [
      {
        "id": "createdAt",
        "title": "تاريخ الإضافة",
        "icon": Icons.calendar_today,
        "description": "ترتيب العقارات حسب تاريخ إضافتها"
      },
      {
        "id": "price",
        "title": "السعر",
        "icon": Icons.monetization_on,
        "description": "ترتيب العقارات حسب السعر"
      },
      {
        "id": "area",
        "title": "المساحة",
        "icon": Icons.square_foot,
        "description": "ترتيب العقارات حسب المساحة"
      },
      {
        "id": "views",
        "title": "الأكثر مشاهدة",
        "icon": Icons.visibility,
        "description": "ترتيب العقارات حسب عدد المشاهدات"
      },
      {
        "id": "rating",
        "title": "التقييم",
        "icon": Icons.star,
        "description": "ترتيب العقارات حسب التقييم"
      },
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // عنوان الترتيب
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(20),
                        shape: BoxShape.circle),
                      child: Icon(
                        Icons.sort,
                        color: AppColors.primary,
                        size: 20)),
                    const SizedBox(width: 12),
                    const Text(
                      "ترتيب ذكي للعقارات",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints()),
                  ]),

                const SizedBox(height: 16),

                // خيارات الترتيب
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.sort_by_alpha,
                            size: 18,
                            color: AppColors.primary),
                          const SizedBox(width: 8),
                          const Text(
                            "ترتيب حسب:",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14)),
                        ]),
                      const SizedBox(height: 12),

                      // خيارات الترتيب المحسنة
                      ...sortOptions.map((option) {
                        final bool isSelected = tempSortBy == option["id"];
                        return InkWell(
                          onTap: () {
                            setState(() {
                              tempSortBy = option["id"];
                            });
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.primary.withAlpha(20)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isSelected
                                    ? AppColors.primary.withAlpha(50)
                                    : Colors.transparent,
                                width: 1)),
                            child: Row(
                              children: [
                                Icon(
                                  option["icon"] as IconData,
                                  color: isSelected
                                      ? AppColors.primary
                                      : Colors.grey.shade600,
                                  size: 18),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        option["title"] as String,
                                        style: TextStyle(
                                          color: isSelected
                                              ? AppColors.primary
                                              : Colors.black87,
                                          fontWeight: isSelected
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                          fontSize: 14)),
                                      if (isSelected) ...[
                                        const SizedBox(height: 4),
                                        Text(
                                          option["description"] as String,
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 12)),
                                      ],
                                    ])),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: AppColors.primary,
                                    size: 18),
                              ])));
                      }),
                    ])),

                const SizedBox(height: 16),

                // اتجاه الترتيب
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.swap_vert,
                            size: 18,
                            color: AppColors.primary),
                          const SizedBox(width: 8),
                          const Text(
                            "اتجاه الترتيب:",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14)),
                        ]),
                      const SizedBox(height: 12),

                      // خيارات اتجاه الترتيب
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  tempSortAscending = true;
                                });
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: tempSortAscending
                                      ? AppColors.primary.withAlpha(20)
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: tempSortAscending
                                        ? AppColors.primary.withAlpha(50)
                                        : Colors.grey.shade300,
                                    width: 1)),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.arrow_upward,
                                      color: tempSortAscending
                                          ? AppColors.primary
                                          : Colors.grey.shade600,
                                      size: 20),
                                    const SizedBox(height: 8),
                                    Text(
                                      "تصاعدي",
                                      style: TextStyle(
                                        color: tempSortAscending
                                            ? AppColors.primary
                                            : Colors.black87,
                                        fontWeight: tempSortAscending
                                            ? FontWeight.bold
                                            : FontWeight.normal)),
                                    const SizedBox(height: 4),
                                    Text(
                                      "من الأقل إلى الأعلى",
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 10),
                                      textAlign: TextAlign.center),
                                  ])))),
                          const SizedBox(width: 12),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  tempSortAscending = false;
                                });
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: !tempSortAscending
                                      ? AppColors.primary.withAlpha(20)
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: !tempSortAscending
                                        ? AppColors.primary.withAlpha(50)
                                        : Colors.grey.shade300,
                                    width: 1)),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.arrow_downward,
                                      color: !tempSortAscending
                                          ? AppColors.primary
                                          : Colors.grey.shade600,
                                      size: 20),
                                    const SizedBox(height: 8),
                                    Text(
                                      "تنازلي",
                                      style: TextStyle(
                                        color: !tempSortAscending
                                            ? AppColors.primary
                                            : Colors.black87,
                                        fontWeight: !tempSortAscending
                                            ? FontWeight.bold
                                            : FontWeight.normal)),
                                    const SizedBox(height: 4),
                                    Text(
                                      "من الأعلى إلى الأقل",
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 10),
                                      textAlign: TextAlign.center),
                                  ])))),
                        ]),
                    ])),

                const SizedBox(height: 20),

                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر إعادة الضبط
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          tempSortBy = "createdAt";
                          tempSortAscending = false;
                        });
                      },
                      icon: const Icon(Icons.refresh, size: 18),
                      label: const Text("إعادة ضبط"),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey.shade700)),

                    Row(
                      children: [
                        // زر الإلغاء
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.grey.shade700),
                          child: const Text("إلغاء")),
                        const SizedBox(width: 8),
                        // زر التطبيق
                        ElevatedButton(
                          onPressed: () {
                            // تطبيق الترتيب
                            setState(() {
                              _sortBy = tempSortBy;
                              _sortAscending = tempSortAscending;
                            });

                            Navigator.pop(context);
                            _loadEstates(refresh: true);

                            // عرض رسالة تأكيد
                            final sortOption = sortOptions.firstWhere(
                              (option) => option["id"] == _sortBy,
                              orElse: () => <String, dynamic>{
                                "id": "createdAt",
                                "title": "تاريخ الإضافة",
                                "icon": Icons.calendar_today,
                                "description": "ترتيب العقارات حسب تاريخ إضافتها"
                              });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  "تم ترتيب العقارات حسب ${sortOption["title"]} بشكل ${_sortAscending ? "تصاعدي" : "تنازلي"}"),
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 2)));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 10),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8))),
                          child: const Text("تطبيق")),
                      ]),
                  ]),
              ])))));
  }

  /// يبني المحتوى الخاص بالصفحة حسب البحث والقسم المختار.
  Widget _buildSectionContent() {
    // إذا كان هناك بحث
    if (_searchQuery.trim().isNotEmpty) {
      return BlocConsumer<EstateBloc, EstateState>(
        listener: (context, state) {
          if (state is EstateLoading) {
            if (mounted) {
              setState(() {
                _isLoading = true;
              });
            }
          } else {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is PaginatedEstatesLoaded) {
              if (mounted) {
                setState(() {
                  _hasMoreData = state.hasMoreData;
                });
              }
            } else if (state is EstateError) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red));
              }
            }
          }
        },
        builder: (context, state) {
          if (state is EstateLoading && state.isFirstFetch) {
            return const LoadingWidget(isListView: false);
          } else if (state is PaginatedEstatesLoaded) {
            final query = _searchQuery.toLowerCase();
            final searchResults = state.estates.where((estate) {
              final titleMatch = estate.title.toLowerCase().contains(query);
              final locationMatch =
                  estate.location.toLowerCase().contains(query);
              final descriptionMatch =
                  estate.description.toLowerCase().contains(query);
              return titleMatch || locationMatch || descriptionMatch;
            }).toList();

            if (searchResults.isEmpty) {
              return EmptyStateWidget(
                title: "لا توجد نتائج مطابقة",
                description:
                    "حاول تغيير كلمات البحث أو تصفح العقارات في أقسام أخرى",
                icon: Icons.search_off,
                buttonText: "مسح البحث",
                onButtonPressed: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = "";
                  });
                });
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان نتائج البحث
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "نتائج البحث عن: $_searchQuery",
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary)),
                    Text(
                      "${searchResults.length} نتيجة",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600)),
                  ]),

                const SizedBox(height: 16),

                // عرض نتائج البحث في شبكة
                AnimationLimiter(
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12),
                    itemCount: searchResults.length,
                    itemBuilder: (context, index) {
                      return AnimationConfiguration.staggeredGrid(
                        position: index,
                        duration: const Duration(milliseconds: 500),
                        columnCount: 2,
                        child: ScaleAnimation(
                          child: FadeInAnimation(
                            child: EstateCard(estate: searchResults[index]))));
                    })),
              ]);
          } else if (state is EstateError) {
            return EmptyStateWidget(
              title: "حدث خطأ",
              description: state.message,
              icon: Icons.error_outline,
              iconColor: Colors.red,
              buttonText: "إعادة المحاولة",
              onButtonPressed: () {
                _loadEstates(refresh: true);
              });
          }
          return const LoadingWidget(isListView: false);
        });
    }

    // إذا كان القسم المحدد هو الرئيسية أو الكل
    if (selectedCategory == "الرئيسية" || selectedCategory == "الكل") {
      return BlocConsumer<EstateBloc, EstateState>(
        listener: (context, state) {
          if (state is EstateLoading) {
            if (mounted) {
              setState(() {
                _isLoading = true;
              });
            }
          } else {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is PaginatedEstatesLoaded) {
              if (mounted) {
                setState(() {
                  _hasMoreData = state.hasMoreData;
                });
              }
            } else if (state is EstateError) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red));
              }
            }
          }
        },
        builder: (context, state) {
          if (state is EstateLoading && state.isFirstFetch) {
            return const LoadingWidget(isListView: true);
          } else if (state is PaginatedEstatesLoaded) {
            final allEstates = state.estates;

            if (allEstates.isEmpty) {
              return EmptyStateWidget(
                title: "لا توجد إعلانات حالياً",
                description: _isUserSeeker
                    ? "سيتم إضافة إعلانات جديدة قريباً، يمكنك تصفح العقارات المتاحة"
                    : "سيتم إضافة إعلانات جديدة قريباً، يمكنك إضافة إعلان جديد بالضغط على زر الإضافة",
                icon: Icons.home_work,
                buttonText: _isUserSeeker ? null : "إضافة إعلان جديد",
                onButtonPressed: _isUserSeeker ? null : () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const ImprovedCategorySelectionPage()));
                });
            }

            // استخدام الخوارزمية المتقدمة للحصول على الإعلانات المحسنة
            final optimizedEstates = _getOptimizedEstatesSync(allEstates);

            // إعلانات VIP (محسنة)
            final vipEstates = optimizedEstates
                .where((e) => e.vipBadge == true)
                .take(10)
                .toList();

            // إعلانات مميزة (محسنة)
            final featuredEstates = optimizedEstates
                .where((e) => e.isFeatured)
                .take(10)
                .toList();

            // إعلانات مثبتة (محسنة)
            final pinnedEstates = optimizedEstates
                .where((e) => e.kuwaitCornersPin == true)
                .take(8)
                .toList();

            // إعلانات مثبتة في الصفحة الرئيسية (محسنة)
            final pinnedOnHomeEstates = optimizedEstates
                .where((e) => e.pinnedOnHome == true)
                .take(6)
                .toList();

            // إعلانات متحركة (محسنة)
            final movingEstates = optimizedEstates
                .where((e) => e.movingAd == true)
                .take(5)
                .toList();

            // أحدث الإعلانات (محسنة)
            final latestEstates = optimizedEstates.take(15).toList();

            // إعلانات تجارية
            final commercialEstates = allEstates
                .where((e) => e.mainCategory?.toLowerCase() == 'commercial')
                .toList();

            // إعلانات حسب المميزات
            final garageEstates =
                allEstates.where((e) => e.hasGarage == true).toList();
            final centralACEstates =
                allEstates.where((e) => e.hasCentralAC == true).toList();
            final maidRoomEstates =
                allEstates.where((e) => e.hasMaidRoom == true).toList();

            // إعلانات حسب نوع المعلن
            final companyEstates = allEstates
                .where((e) => e.postedByUserType?.toLowerCase() == 'company')
                .toList();
            final investorEstates = allEstates
                .where((e) => e.postedByUserType?.toLowerCase() == 'investor')
                .toList();

            // سيتم تحميل العقارات المنسوخة بشكل منفصل

            return AnimationLimiter(
              child: Column(
                children: [
                  // إعلانات VIP
                  if (vipEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 0,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات VIP",
                            estates: vipEstates,
                            icon: Icons.workspace_premium,
                            color: const Color(0xFFD4AF37))))),

                  // إعلانات مميزة
                  if (featuredEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 1,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات مميزة",
                            estates: featuredEstates,
                            icon: Icons.star,
                            color: Colors.amber)))),

                  // العقارات المنسوخة
                  _buildCopiedEstatesSection(),

                  // إعلانات مثبتة
                  if (pinnedEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 2,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات مثبتة",
                            estates: pinnedEstates,
                            icon: Icons.push_pin,
                            color: Colors.blue)))),

                  // إعلانات متحركة
                  if (movingEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 3,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات متحركة",
                            estates: movingEstates,
                            icon: Icons.animation,
                            color: Colors.purple)))),

                  // إعلانات مثبتة في الصفحة الرئيسية
                  if (pinnedOnHomeEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 4,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات مثبتة في الصفحة الرئيسية",
                            estates: pinnedOnHomeEstates,
                            icon: Icons.home,
                            color: Colors.teal)))),

                  // أحدث الإعلانات
                  if (latestEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 5,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: selectedCategory == "الكل"
                                ? "جميع الإعلانات"
                                : "أحدث الإعلانات",
                            estates: latestEstates,
                            icon: selectedCategory == "الكل"
                                ? Icons.home_work
                                : Icons.new_releases,
                            color: AppColors.primary)))),
                  if (commercialEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 5,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات تجارية",
                            estates: commercialEstates,
                            icon: Icons.business,
                            color: Colors.blue)))),
                  if (garageEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 6,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات تحتوي مرآب",
                            estates: garageEstates,
                            icon: Icons.garage,
                            color: Colors.green)))),
                  if (centralACEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 7,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات تحتوي تكييف مركزي",
                            estates: centralACEstates,
                            icon: Icons.ac_unit,
                            color: Colors.lightBlue)))),
                  if (maidRoomEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 8,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات تحتوي غرفة خادمة",
                            estates: maidRoomEstates,
                            icon: Icons.bedroom_child,
                            color: Colors.purple)))),
                  if (companyEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 9,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات شركات عقارات",
                            estates: companyEstates,
                            icon: Icons.apartment,
                            color: Colors.indigo)))),
                  if (investorEstates.isNotEmpty)
                    AnimationConfiguration.staggeredList(
                      position: 10,
                      duration: const Duration(milliseconds: 500),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildHorizontalSection(
                            title: "إعلانات مستثمرين",
                            estates: investorEstates,
                            icon: Icons.attach_money,
                            color: Colors.teal)))),
                ]));
          } else if (state is EstateError) {
            return EmptyStateWidget(
              title: "حدث خطأ",
              description: state.message,
              icon: Icons.error_outline,
              iconColor: Colors.red,
              buttonText: "إعادة المحاولة",
              onButtonPressed: () {
                _loadEstates(refresh: true);
              });
          }
          return const LoadingWidget(isListView: true);
        });
    }
    // إذا كان القسم المحدد هو التفضيلات
    else if (selectedCategory == "التفضيلات") {
      return EmptyStateWidget(
        title: "لا توجد إعلانات عقارية مفضلة",
        description:
            "يمكنك إضافة العقارات إلى المفضلة بالضغط على أيقونة القلب في بطاقة العقار",
        icon: Icons.favorite_border,
        iconColor: Colors.red,
        buttonText: "استعرض العقارات",
        onButtonPressed: () {
          setState(() {
            selectedCategory = "الرئيسية";
          });
        });
    }
    // إذا كان القسم المحدد هو أي قسم آخر
    else {
      return BlocConsumer<EstateBloc, EstateState>(
        listener: (context, state) {
          if (state is EstateLoading) {
            if (mounted) {
              setState(() {
                _isLoading = true;
              });
            }
          } else {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is PaginatedEstatesLoaded) {
              if (mounted) {
                setState(() {
                  _hasMoreData = state.hasMoreData;
                });

                // حفظ البيانات في التخزين المؤقت
                _cacheLoadedData(state.estates, state.hasMoreData);
              }
            } else if (state is EstateError) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red));
              }
            }
          }
        },
        builder: (context, state) {
          if (state is EstateLoading && state.isFirstFetch) {
            return const LoadingWidget(isListView: false);
          } else if (state is PaginatedEstatesLoaded) {
            // تحسين الفلترة لتكون أكثر دقة
            final filteredEstates = _filterEstatesByCategory(state.estates, selectedCategory);

            if (filteredEstates.isEmpty) {
              return EmptyStateWidget(
                title: "لا توجد إعلانات في قسم $selectedCategory",
                description: _isUserSeeker
                    ? "سيتم إضافة إعلانات جديدة قريباً، يمكنك تصفح الأقسام الأخرى"
                    : "سيتم إضافة إعلانات جديدة قريباً، يمكنك إضافة إعلان جديد بالضغط على زر الإضافة",
                icon: Icons.category,
                buttonText: _isUserSeeker ? null : "إضافة إعلان جديد",
                onButtonPressed: _isUserSeeker ? null : () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const ImprovedCategorySelectionPage()));
                });
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان القسم
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "عقارات $selectedCategory",
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary)),
                    Text(
                      "${filteredEstates.length} عقار",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600)),
                  ]),

                const SizedBox(height: 16),

                // عرض العقارات في شبكة مع التحميل الكسول
                LazyLoadingGridWidget<Estate>(
                  loadData: _loadEstatesPage,
                  itemBuilder: (context, estate, index) {
                    return AnimationConfiguration.staggeredGrid(
                      position: index,
                      duration: const Duration(milliseconds: 500),
                      columnCount: 2,
                      child: ScaleAnimation(
                        child: FadeInAnimation(
                          child: EstateCard(estate: estate))));
                  },
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  itemsPerPage: 20,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                ),
              ]);
          } else if (state is EstateError) {
            return EmptyStateWidget(
              title: "حدث خطأ",
              description: state.message,
              icon: Icons.error_outline,
              iconColor: Colors.red,
              buttonText: "إعادة المحاولة",
              onButtonPressed: () {
                _loadEstates(refresh: true);
              });
          }
          return const LoadingWidget(isListView: false);
        });
    }
  }

  /// يبني قسم عرض الإعلانات الأفقية لقسم معين.
  Widget _buildHorizontalSection({
    required String title,
    required List<Estate> estates,
    IconData? icon,
    Color? color,
  }) {
    final cardWidth = ResponsiveUtils.getCardWidth(context);
    final sectionHeight = ResponsiveUtils.getCardHeight(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(context);
    final spacing = ResponsiveUtils.getSpacing(context, SpacingType.medium);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة وزر "عرض الكل"
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: spacing / 2,
            vertical: spacing),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  if (icon != null)
                    ResponsiveIcon(
                      icon,
                      sizeType: IconSizeType.medium,
                      color: color ?? AppColors.primary),
                  SizedBox(width: spacing / 2),
                  ResponsiveText(
                    title,
                    sizeType: FontSizeType.large,
                    fontWeight: FontWeight.bold,
                    color: color ?? AppColors.primary),
                ]),
              TextButton.icon(
                onPressed: () {
                  // تحديد نوع الإعلانات المطلوب عرضها
                  String estateType = "latest"; // افتراضي

                  if (title == "إعلانات مميزة") {
                    estateType = "featured";
                  } else if (title == "إعلانات تجارية") {
                    estateType = "commercial";
                  } else if (title.contains("مرآب")) {
                    estateType = "garage";
                  } else if (title.contains("تكييف مركزي")) {
                    estateType = "centralAC";
                  } else if (title.contains("غرفة خادمة")) {
                    estateType = "maidRoom";
                  } else if (title.contains("شركات")) {
                    estateType = "company";
                  } else if (title.contains("مستثمرين")) {
                    estateType = "investor";
                  }

                  // الانتقال إلى صفحة عرض كل الإعلانات
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => AllEstatesPage(
                        title: title,
                        estateType: estateType,
                        initialEstates: estates)));
                },
                icon: ResponsiveIcon(
                  Icons.arrow_forward,
                  sizeType: IconSizeType.small,
                  color: color ?? AppColors.primary),
                label: ResponsiveText(
                  "عرض الكل",
                  sizeType: FontSizeType.medium,
                  color: color ?? AppColors.primary),
                style: TextButton.styleFrom(
                  foregroundColor: color ?? AppColors.primary,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  shadowColor: Colors.transparent)),
            ])),

        // قائمة أفقية لبطاقات الإعلانات مع تأثيرات حركية
        SizedBox(
          height: sectionHeight,
          child: AnimationLimiter(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: estates.length > 10 ? 10 : estates.length,
              itemBuilder: (context, index) {
                final estate = estates[index];
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 500),
                  child: SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: Container(
                        width: cardWidth,
                        margin: EdgeInsets.only(
                          right: spacing,
                          bottom: spacing / 2),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(borderRadius),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(borderRadius),
                          child: PremiumEstateCard(
                            estate: estate,
                            cardType: _getCardTypeForEstate(estate, title),
                            size: Size(cardWidth, sectionHeight - 30),
                            isHorizontal: true))))));
              }))),
      ]);
  }

  /// الانتقال لصفحة البحث المتقدم
  void _navigateToAdvancedSearch() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdvancedSearchPage()));
  }

  /// الانتقال لصفحة المفضلة
  void _navigateToFavorites() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FavoritesPage()));
  }

  /// معالجة النقر على أدوات شريط الأدوات المخصص
  void _handleToolTap(Map<String, dynamic> tool) {
    final route = tool['route'] as String?;

    if (route == null) return;

    switch (route) {
      case '/favorites':
        _navigateToFavorites();
        break;
      case '/comparison':
        // _navigateToComparison(); // معلق مؤقتاً
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('المقارنة معطلة مؤقتاً'),
            backgroundColor: Colors.orange));
        break;
      case '/advanced-search':
        _navigateToAdvancedSearch();
        break;
      case '/add-property':
        _navigateToAddProperty();
        break;
      case '/my-properties':
        _navigateToMyProperties();
        break;
      case '/my-clients':
        _navigateToMyClients();
        break;
      case '/agent-stats':
        _navigateToAgentStats();
        break;
      case '/inquiries':
        _navigateToInquiries();
        break;
      case '/property-stats':
        _navigateToPropertyStats();
        break;
      case '/company-projects':
        _navigateToCompanyProjects();
        break;
      case '/company-clients':
        _navigateToCompanyClients();
        break;
      case '/market-analysis':
        _navigateToMarketAnalysis();
        break;
      case '/reports':
        _navigateToReports();
        break;
      case '/teams':
        _navigateToTeams();
        break;
      case '/add-project':
        _navigateToAddProject();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('الصفحة $route غير متوفرة حالياً'),
            backgroundColor: Colors.orange));
    }
  }



  /// الانتقال لصفحة إضافة عقار
  void _navigateToAddProperty() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ImprovedAdCreationEntry()));
  }

  /// الانتقال لصفحة عقاراتي
  void _navigateToMyProperties() {
    Navigator.pushNamed(context, '/my-properties');
  }

  /// الانتقال لصفحة عملائي
  void _navigateToMyClients() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة العملاء ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة إحصائيات الوسيط
  void _navigateToAgentStats() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة الإحصائيات ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة الاستفسارات
  void _navigateToInquiries() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة الاستفسارات ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة إحصائيات العقارات
  void _navigateToPropertyStats() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة إحصائيات العقارات ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة مشاريع الشركة
  void _navigateToCompanyProjects() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة المشاريع ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة عملاء الشركة
  void _navigateToCompanyClients() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة عملاء الشركة ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة تحليل السوق
  void _navigateToMarketAnalysis() {
    Navigator.pushNamed(context, '/market-analysis');
  }

  /// الانتقال لصفحة التقارير
  void _navigateToReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة التقارير ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة الفرق
  void _navigateToTeams() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة الفرق ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  /// الانتقال لصفحة إضافة مشروع
  void _navigateToAddProject() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة إضافة المشروع ستكون متوفرة قريباً'),
        backgroundColor: Colors.blue));
  }

  // دالة المقارنة - معلقة مؤقتاً
  /*
  /// الانتقال لصفحة المقارنة
  Future<void> _navigateToComparison() async {
    try {
      final comparisonProperties = await _comparisonService.getComparisonProperties();

      if (comparisonProperties.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد عقارات في المقارنة'),
              backgroundColor: Colors.orange));
        }
        return;
      }

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PropertyComparisonPage(
              properties: comparisonProperties)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red));
      }
    }
  }
  */

  /// الحصول على الإعلانات المحسنة للصفحة الرئيسية (نسخة متزامنة)
  List<Estate> _getOptimizedEstatesSync(List<Estate> allEstates) {
    try {
      // استخدام الترتيب المحسن بدلاً من الخوارزمية غير المتزامنة
      return _fallbackSorting(allEstates);
    } catch (e) {
      // في حالة الخطأ، إرجاع القائمة كما هي
      return allEstates;
    }
  }

  /// ترتيب احتياطي في حالة فشل الخوارزمية المتقدمة
  List<Estate> _fallbackSorting(List<Estate> estates) {
    final sortedEstates = [...estates];

    // ترتيب حسب الأولوية: VIP > مثبت في الرئيسية > مميز > مثبت > متحرك > عادي
    sortedEstates.sort((a, b) {
      // حساب نقاط الأولوية
      int scoreA = _calculateFallbackScore(a);
      int scoreB = _calculateFallbackScore(b);

      if (scoreA != scoreB) {
        return scoreB.compareTo(scoreA); // الأعلى نقاطاً أولاً
      }

      // في حالة التساوي، ترتيب حسب التاريخ
      return b.createdAt.compareTo(a.createdAt);
    });

    return sortedEstates;
  }

  /// حساب نقاط الأولوية للترتيب الاحتياطي
  int _calculateFallbackScore(Estate estate) {
    int score = 0;

    if (estate.vipBadge == true) score += 1000;
    if (estate.pinnedOnHome == true) score += 800;
    if (estate.isFeatured == true) score += 600;
    if (estate.kuwaitCornersPin == true) score += 400;
    if (estate.movingAd == true) score += 200;

    // نقاط إضافية للحداثة
    final hoursSinceCreation = DateTime.now().difference(estate.createdAt).inHours;
    if (hoursSinceCreation <= 24) {
      score += 50;
    } else if (hoursSinceCreation <= 72) {
      score += 30;
    }

    return score;
  }

  /// بناء قسم العقارات المنسوخة
  Widget _buildCopiedEstatesSection() {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('estates')
          .where('isCopied', isEqualTo: true)
          .where('isPaid', isEqualTo: true)
          .where('isPaymentVerified', isEqualTo: true)
          .where('mainCategory', isEqualTo: 'عقار للايجار')
          .orderBy('copiedAt', descending: true)
          .limit(8)
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return const SizedBox.shrink();
        }

        final copiedDocs = snapshot.data!.docs;
        final copiedEstates = <Estate>[];

        for (final doc in copiedDocs) {
          try {
            final estateBase = EstateFactory.createFromSnapshot(doc);
            final estate = EstateConverter.toLegacyEstate(estateBase);
            if (estate != null) {
              copiedEstates.add(estate);
            }
          } catch (e) {
            // تجاهل العقارات التي لا يمكن تحويلها
          }
        }

        if (copiedEstates.isEmpty) {
          return const SizedBox.shrink();
        }

        return AnimationConfiguration.staggeredList(
          position: 2,
          duration: const Duration(milliseconds: 500),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: _buildCopiedHorizontalSection(
                title: "إيجار منسوخ",
                copiedEstates: copiedEstates,
                copiedDocs: copiedDocs,
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم العقارات المنسوخة الأفقي
  Widget _buildCopiedHorizontalSection({
    required String title,
    required List<Estate> copiedEstates,
    required List<QueryDocumentSnapshot> copiedDocs,
  }) {
    final cardWidth = ResponsiveUtils.getCardWidth(context);
    final sectionHeight = ResponsiveUtils.getCardHeight(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(context);
    final spacing = ResponsiveUtils.getSpacing(context, SpacingType.medium);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة وزر "عرض الكل"
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: spacing / 2,
            vertical: spacing),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  ResponsiveIcon(
                    Icons.content_copy,
                    sizeType: IconSizeType.medium,
                    color: Colors.green),
                  SizedBox(width: spacing / 2),
                  ResponsiveText(
                    title,
                    sizeType: FontSizeType.large,
                    fontWeight: FontWeight.bold,
                    color: Colors.green),
                ]),
              TextButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/copied-properties');
                },
                icon: ResponsiveIcon(
                  Icons.arrow_forward,
                  sizeType: IconSizeType.small,
                  color: Colors.green),
                label: ResponsiveText(
                  "عرض الكل",
                  sizeType: FontSizeType.medium,
                  color: Colors.green),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.green,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  shadowColor: Colors.transparent)),
            ])),

        // قائمة أفقية لبطاقات العقارات المنسوخة
        SizedBox(
          height: sectionHeight,
          child: AnimationLimiter(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: copiedEstates.length,
              itemBuilder: (context, index) {
                final estate = copiedEstates[index];
                final docData = copiedDocs[index].data() as Map<String, dynamic>;

                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 500),
                  child: SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: Container(
                        width: cardWidth,
                        margin: EdgeInsets.only(
                          right: spacing,
                          bottom: spacing / 2),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(borderRadius),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 4)),
                          ]),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(borderRadius),
                          child: _buildCopiedEstateCard(estate, docData))))));
              }))),
      ]);
  }

  /// بناء بطاقة العقار المنسوخ مع شارة مميزة
  Widget _buildCopiedEstateCard(Estate estate, Map<String, dynamic> docData) {
    final images = docData['images'] as List<dynamic>? ?? [];
    final imageUrls = images.map((e) => e.toString()).toList();

    return GestureDetector(
      onTap: () => _navigateToCopiedEstateDetails(estate, docData),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار مع الشارات
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: imageUrls.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: imageUrls.first,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Icon(Icons.error),
                            ),
                          )
                        : Container(
                            color: Colors.grey[200],
                            child: Icon(Icons.home_outlined, size: 40, color: Colors.grey[400]),
                          ),
                  ),
                ),
                // شارة "منسوخ"
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.content_copy, size: 12, color: Colors.white),
                        const SizedBox(width: 4),
                        Text(
                          'منسوخ',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // مؤشر عدد الصور
                if (imageUrls.length > 1)
                  Positioned(
                    bottom: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${imageUrls.length}',
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            // معلومات العقار
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      estate.title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 12, color: AppColors.textSecondary),
                        const SizedBox(width: 2),
                        Expanded(
                          child: Text(
                            estate.location,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      '${estate.price.toStringAsFixed(0)} د.ك',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الانتقال لصفحة تفاصيل العقار المنسوخ
  void _navigateToCopiedEstateDetails(Estate estate, Map<String, dynamic> docData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CopiedEstateDetailsPage(
          estate: estate,
          copiedData: docData,
        ),
      ),
    );
  }
}
