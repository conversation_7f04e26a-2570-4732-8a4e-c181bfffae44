import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_colors.dart';

/// صفحة تشخيص مشاكل الإعلانات
class AdsDiagnosisPage extends StatefulWidget {
  const AdsDiagnosisPage({super.key});

  @override
  State<AdsDiagnosisPage> createState() => _AdsDiagnosisPageState();
}

class _AdsDiagnosisPageState extends State<AdsDiagnosisPage> {
  Map<String, dynamic> diagnosisData = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _runDiagnosis();
  }

  Future<void> _runDiagnosis() async {
    setState(() {
      isLoading = true;
    });

    try {
      final data = <String, dynamic>{};

      // إجمالي العقارات
      final totalSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .get();
      data['totalEstates'] = totalSnapshot.docs.length;

      // العقارات المدفوعة (هذا هو المعيار الوحيد لظهور الإعلانات)
      final paidSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .get();
      data['paidEstates'] = paidSnapshot.docs.length;

      // العقارات غير المدفوعة
      final unpaidSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: false)
          .get();
      data['unpaidEstates'] = unpaidSnapshot.docs.length;

      // العقارات المميزة (من المدفوعة)
      final featuredSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .where('isFeatured', isEqualTo: true)
          .get();
      data['featuredEstates'] = featuredSnapshot.docs.length;

      // العقارات VIP (من المدفوعة)
      final vipSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .where('vipBadge', isEqualTo: true)
          .get();
      data['vipEstates'] = vipSnapshot.docs.length;

      // العقارات المثبتة (من المدفوعة)
      final pinnedSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .where('isPaymentVerified', isEqualTo: true)
          .where('kuwaitCornersPin', isEqualTo: true)
          .get();
      data['pinnedEstates'] = pinnedSnapshot.docs.length;

      // أحدث 10 عقارات
      final latestSnapshot = await FirebaseFirestore.instance
          .collection('estates')
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      data['latestEstates'] = latestSnapshot.docs.map((doc) {
        final docData = doc.data();
        return {
          'id': doc.id,
          'title': docData['title'] ?? 'بدون عنوان',
          'isPaymentVerified': docData['isPaymentVerified'],
          'isFeatured': docData['isFeatured'],
          'vipBadge': docData['vipBadge'],
          'kuwaitCornersPin': docData['kuwaitCornersPin'],
          'createdAt': docData['createdAt']?.toString() ?? 'غير محدد',
          'rawData': {
            'isPaymentVerified_exists': docData.containsKey('isPaymentVerified'),
            'isPaymentVerified_value': docData['isPaymentVerified'],
            'isFeatured_exists': docData.containsKey('isFeatured'),
            'isFeatured_value': docData['isFeatured'],
          }
        };
      }).toList();

      setState(() {
        diagnosisData = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        diagnosisData = {'error': e.toString()};
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تشخيص الإعلانات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runDiagnosis,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : diagnosisData.containsKey('error')
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        'حدث خطأ',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        diagnosisData['error'],
                        style: GoogleFonts.cairo(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatisticsCard(),
                      const SizedBox(height: 16),
                      _buildLatestEstatesCard(),
                      const SizedBox(height: 16),
                      _buildRecommendationsCard(),
                      const SizedBox(height: 16),
                      _buildFixActionsCard(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الإعلانات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatItem('إجمالي العقارات', diagnosisData['totalEstates'] ?? 0),
            _buildStatItem('العقارات المدفوعة (تظهر في التطبيق)', diagnosisData['paidEstates'] ?? 0, Colors.green),
            _buildStatItem('العقارات غير المدفوعة (لا تظهر)', diagnosisData['unpaidEstates'] ?? 0, Colors.red),
            _buildStatItem('العقارات المميزة', diagnosisData['featuredEstates'] ?? 0, Colors.orange),
            _buildStatItem('العقارات VIP', diagnosisData['vipEstates'] ?? 0, Colors.purple),
            _buildStatItem('العقارات المثبتة', diagnosisData['pinnedEstates'] ?? 0, Colors.blue),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, int value, [Color? color]) {
    final itemColor = color ?? AppColors.primary;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: GoogleFonts.cairo(fontSize: 14),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: itemColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              value.toString(),
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: itemColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLatestEstatesCard() {
    final latestEstates = diagnosisData['latestEstates'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أحدث 10 عقارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            if (latestEstates.isEmpty)
              Text(
                'لا توجد عقارات',
                style: GoogleFonts.cairo(color: Colors.grey),
              )
            else
              ...latestEstates.map((estate) => _buildEstateItem(estate)),
          ],
        ),
      ),
    );
  }

  Widget _buildEstateItem(Map<String, dynamic> estate) {
    final rawData = estate['rawData'] as Map<String, dynamic>? ?? {};

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            estate['title'] ?? 'بدون عنوان',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'ID: ${estate['id']}',
            style: GoogleFonts.cairo(
              fontSize: 10,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatusChip('مدفوع', estate['isPaymentVerified'] == true, Colors.green),
              const SizedBox(width: 4),
              _buildStatusChip('مميز', estate['isFeatured'] == true, Colors.orange),
              const SizedBox(width: 4),
              _buildStatusChip('VIP', estate['vipBadge'] == true, Colors.purple),
              const SizedBox(width: 4),
              _buildStatusChip('مثبت', estate['kuwaitCornersPin'] == true, Colors.red),
            ],
          ),
          const SizedBox(height: 8),
          // عرض البيانات الخام
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'البيانات الخام:',
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'isPaymentVerified موجود: ${rawData['isPaymentVerified_exists'] ?? 'غير محدد'}',
                  style: GoogleFonts.cairo(fontSize: 9),
                ),
                Text(
                  'isPaymentVerified قيمة: ${rawData['isPaymentVerified_value']}',
                  style: GoogleFonts.cairo(
                    fontSize: 9,
                    color: rawData['isPaymentVerified_value'] == true ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'isFeatured موجود: ${rawData['isFeatured_exists'] ?? 'غير محدد'}',
                  style: GoogleFonts.cairo(fontSize: 9),
                ),
                Text(
                  'isFeatured قيمة: ${rawData['isFeatured_value']}',
                  style: GoogleFonts.cairo(fontSize: 9),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, bool isActive, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isActive ? color.withValues(alpha: 0.2) : Colors.grey.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: GoogleFonts.cairo(
          fontSize: 10,
          color: isActive ? color : Colors.grey,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRecommendationsCard() {
    final totalEstates = diagnosisData['totalEstates'] ?? 0;
    final paidEstates = diagnosisData['paidEstates'] ?? 0;
    final unpaidEstates = diagnosisData['unpaidEstates'] ?? 0;

    final recommendations = <String>[];

    if (totalEstates == 0) {
      recommendations.add('لا توجد عقارات في قاعدة البيانات');
    } else if (paidEstates == 0) {
      recommendations.add('لا توجد عقارات مدفوعة - تحقق من حقل isPaymentVerified');
      recommendations.add('استخدم زر "تفعيل الدفع للجميع" لإصلاح هذه المشكلة');
    } else if (unpaidEstates > 0) {
      recommendations.add('يوجد $unpaidEstates عقار غير مدفوع - لن تظهر في التطبيق');
      recommendations.add('يمكنك تفعيل الدفع لجميع العقارات باستخدام الأدوات أدناه');
    }

    if (recommendations.isEmpty) {
      recommendations.add('جميع الإعدادات تبدو صحيحة! ✅');
      recommendations.add('جميع العقارات مدفوعة وستظهر في التطبيق');
    }

    // إضافة معلومات إضافية
    if (totalEstates > 0) {
      final percentage = ((paidEstates / totalEstates) * 100).round();
      recommendations.add('نسبة العقارات المدفوعة: $percentage%');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التوصيات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            ...recommendations.map((rec) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    rec.contains('✅') ? Icons.check_circle :
                    rec.contains('%') ? Icons.info :
                    rec.contains('استخدم') ? Icons.build : Icons.warning,
                    size: 16,
                    color: rec.contains('✅') ? Colors.green :
                           rec.contains('%') ? Colors.blue :
                           rec.contains('استخدم') ? Colors.purple : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      rec,
                      style: GoogleFonts.cairo(fontSize: 14),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildFixActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أدوات الإصلاح',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'استخدم هذه الأدوات لإصلاح مشاكل الإعلانات مباشرة:',
              style: GoogleFonts.cairo(fontSize: 14),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _fixAllEstatesPaymentStatus,
                icon: const Icon(Icons.payment),
                label: Text(
                  'تفعيل الدفع لجميع العقارات',
                  style: GoogleFonts.cairo(),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshDiagnosis,
                icon: const Icon(Icons.refresh),
                label: Text(
                  'تحديث التشخيص',
                  style: GoogleFonts.cairo(),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _fixAllEstatesPaymentStatus() async {
    try {
      // عرض تأكيد
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'تأكيد العملية',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'هل تريد تفعيل الدفع لجميع العقارات؟\n\nهذا سيجعل جميع العقارات تظهر في التطبيق (تعيين isPaymentVerified = true)',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('تأكيد', style: GoogleFonts.cairo()),
            ),
          ],
        ),
      );

      if (confirm != true) return;

      // تحديث جميع العقارات
      final batch = FirebaseFirestore.instance.batch();
      final snapshot = await FirebaseFirestore.instance
          .collection('estates')
          .get();

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isPaymentVerified': true});
      }

      await batch.commit();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تفعيل الدفع لـ ${snapshot.docs.length} عقار بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
        _runDiagnosis(); // تحديث التشخيص
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _refreshDiagnosis() {
    _runDiagnosis();
  }
}
