import 'package:flutter/material.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Estate Features Test',
      home: EstateTestPage(),
    );
  }
}

class EstateTestPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // إنشاء عقار تجريبي مع مميزات
    final testEstate = Estate(
      id: 'test-estate-1',
      title: 'عقار تجريبي للاختبار',
      description: 'وصف العقار التجريبي',
      price: 100000.0,
      location: 'الكويت',
      photoUrls: ['https://example.com/image1.jpg'],
      isFeatured: false,
      planType: 'free',
      startDate: DateTime.now(),
      endDate: DateTime.now().add(Duration(days: 30)),
      createdAt: DateTime.now(),
      // تعيين المميزات بقيم true للاختبار
      hasCentralAC: true,
      hasMaidRoom: true,
      hasGarage: true,
      hasSecurity: true,
      hasElevator: true,
      hasSwimmingPool: true,
      hasBalcony: true,
      isFullyFurnished: true,
      allowPets: true,
      hasGarden: true,
      hasPool: true,
      hasDriverRoom: true,
      hasPrivateEntrance: true,
      hasEquippedKitchen: true,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار مميزات العقار'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بيانات العقار التجريبي:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            _buildFeatureTest('تكييف مركزي', testEstate.hasCentralAC),
            _buildFeatureTest('غرفة خادمة', testEstate.hasMaidRoom),
            _buildFeatureTest('مرآب/كراج', testEstate.hasGarage),
            _buildFeatureTest('أمن وحماية', testEstate.hasSecurity),
            _buildFeatureTest('مصعد', testEstate.hasElevator),
            _buildFeatureTest('مسبح', testEstate.hasSwimmingPool),
            _buildFeatureTest('شرفة', testEstate.hasBalcony),
            _buildFeatureTest('مفروش بالكامل', testEstate.isFullyFurnished),
            _buildFeatureTest('السماح بالحيوانات الأليفة', testEstate.allowPets),
            _buildFeatureTest('حديقة', testEstate.hasGarden),
            _buildFeatureTest('مسبح خاص', testEstate.hasPool),
            _buildFeatureTest('غرفة سائق', testEstate.hasDriverRoom),
            _buildFeatureTest('مدخل خاص', testEstate.hasPrivateEntrance),
            _buildFeatureTest('مطبخ مجهز', testEstate.hasEquippedKitchen),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureTest(String featureName, dynamic value) {
    Color color = Colors.red;
    String status = 'غير متوفر';
    
    if (value == true) {
      color = Colors.green;
      status = 'متوفر';
    } else if (value == false) {
      color = Colors.orange;
      status = 'غير متوفر';
    } else if (value == null) {
      color = Colors.grey;
      status = 'null';
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text('$featureName: $status (القيمة: $value)'),
          ),
        ],
      ),
    );
  }
}
